- ++input_feature_tensors=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400.bin
- ++output_dir=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/test_decode_output
- ++bitstream_name=sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400
- ++codec.group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data
