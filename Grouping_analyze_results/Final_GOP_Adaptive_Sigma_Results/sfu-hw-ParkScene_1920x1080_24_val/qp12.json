{"sequence": "sfu-hw-ParkScene_1920x1080_24_val", "qp": "qp12", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-ParkScene_1920x1080_24_val/qp12/codec_output/sidecar_data/minmax.npy", "n_frames": 33, "n_channels": 186, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 138, 141, 142, 143, 147, 150, 151, 158, 166, 167, 172, 173, 178, 180, 181, 183], "size": 116, "min_range": 0.054937679844442755, "max_range": 2.507535107433796, "mean_range": 0.8117033523490607, "boundaries": [-0.39177591253798516, 2.5076019809781154]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 32, 35, 37, 38, 53, 67, 81, 86, 99, 120, 123, 126, 127, 129, 131, 132, 135, 137, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 163, 168, 170, 171, 175, 182, 184, 185], "size": 46, "min_range": 2.6139637483283877, "max_range": 5.082858886569738, "mean_range": 3.5001430715150805, "boundaries": [2.5076019809781154, 5.4069798744942155]}, {"group_id": 2, "name": "high", "channels": [54, 60, 66, 79, 100, 128, 153, 161, 164, 169, 174, 176], "size": 12, "min_range": 5.444755086675286, "max_range": 8.078279081732035, "mean_range": 6.561899924806009, "boundaries": [5.4069798744942155, 8.306357768010317]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 156, 165, 177, 179], "size": 12, "min_range": 8.64722504466772, "max_range": 15.609376698732376, "mean_range": 11.042249933506051, "boundaries": [8.306357768010317, 15.609376698732376]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 138, 141, 142, 143, 147, 150, 151, 158, 166, 167, 172, 173, 178, 180, 181, 183], "size": 116, "min_range": 0.05700634338427335, "max_range": 2.573342200368643, "mean_range": 0.8483474166837525, "boundaries": [-0.3928213564178624, 2.6135187759954466]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 26, 32, 35, 37, 53, 60, 67, 79, 81, 86, 99, 120, 121, 123, 126, 127, 129, 131, 132, 135, 137, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 163, 168, 170, 171, 175, 182, 184, 185], "size": 47, "min_range": 2.61780827306211, "max_range": 5.399838346987963, "mean_range": 3.696366140539659, "boundaries": [2.6135187759954466, 5.619858908408755]}, {"group_id": 2, "name": "high", "channels": [21, 54, 66, 100, 128, 153, 164, 165, 169, 176], "size": 10, "min_range": 5.6958361081779, "max_range": 8.068533569574356, "mean_range": 6.819978023692966, "boundaries": [5.619858908408755, 8.626199040822065]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 156, 161, 174, 177, 179], "size": 13, "min_range": 8.874107971787453, "max_range": 15.684176981449127, "mean_range": 11.21363101211878, "boundaries": [8.626199040822065, 15.684176981449127]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 17, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 138, 141, 142, 143, 147, 150, 151, 158, 159, 166, 167, 172, 173, 178, 180, 181, 183], "size": 117, "min_range": 0.05286901630461216, "max_range": 2.3530073761940002, "mean_range": 0.7816371759638573, "boundaries": [-0.4211913875160991, 2.401685185960784]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 18, 21, 26, 31, 32, 35, 37, 38, 53, 67, 81, 86, 99, 120, 123, 126, 127, 129, 131, 132, 135, 137, 139, 140, 144, 146, 148, 152, 154, 155, 157, 160, 162, 163, 168, 170, 171, 175, 182, 184, 185], "size": 44, "min_range": 2.4968576431274414, "max_range": 4.774808168411255, "mean_range": 3.323707617141984, "boundaries": [2.401685185960784, 5.224561759437668]}, {"group_id": 2, "name": "high", "channels": [54, 60, 66, 79, 100, 128, 145, 153, 161, 164, 169, 174, 176], "size": 13, "min_range": 5.288540363311768, "max_range": 7.404197454452515, "mean_range": 6.080518988462595, "boundaries": [5.224561759437668, 8.04743833291455]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 156, 165, 177, 179], "size": 12, "min_range": 8.361228942871094, "max_range": 15.534576416015625, "mean_range": 10.831001083056131, "boundaries": [8.04743833291455, 15.534576416015625]}]}]}