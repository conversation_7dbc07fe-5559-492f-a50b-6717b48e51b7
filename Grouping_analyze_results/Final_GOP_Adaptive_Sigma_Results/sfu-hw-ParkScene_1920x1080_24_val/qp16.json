{"sequence": "sfu-hw-ParkScene_1920x1080_24_val", "qp": "qp16", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-ParkScene_1920x1080_24_val/qp16/codec_output/sidecar_data/minmax.npy", "n_frames": 33, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 132, 133, 137, 140, 141, 142, 146, 149, 150, 157, 165, 166, 171, 172, 177, 179, 181], "size": 114, "min_range": 0.17137325700605288, "max_range": 2.507535107433796, "mean_range": 0.8246843404497333, "boundaries": [-0.3698106578301399, 2.5340782304386305]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 32, 35, 37, 38, 53, 67, 81, 86, 99, 120, 123, 126, 127, 129, 130, 131, 134, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 156, 158, 159, 161, 162, 167, 169, 170, 174, 180, 182, 183], "size": 46, "min_range": 2.6139637483283877, "max_range": 5.082858886569738, "mean_range": 3.5001430715150805, "boundaries": [2.5340782304386305, 5.437967118707401]}, {"group_id": 2, "name": "high", "channels": [54, 60, 66, 79, 100, 128, 152, 160, 163, 168, 173, 175], "size": 12, "min_range": 5.444755086675286, "max_range": 8.078279081732035, "mean_range": 6.561899924806009, "boundaries": [5.437967118707401, 8.34185600697617]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 155, 164, 176, 178], "size": 12, "min_range": 8.64722504466772, "max_range": 15.609376698732376, "mean_range": 11.042249933506051, "boundaries": [8.34185600697617, 15.609376698732376]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 137, 140, 141, 142, 146, 149, 150, 157, 165, 166, 171, 172, 177, 179, 181], "size": 115, "min_range": 0.1766024074750021, "max_range": 2.61780827306211, "mean_range": 0.877204258788792, "boundaries": [-0.36976356805879895, 2.641124312431993]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 26, 32, 35, 37, 53, 60, 67, 79, 81, 86, 99, 120, 121, 123, 126, 127, 129, 130, 131, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 156, 158, 159, 161, 162, 167, 169, 170, 174, 180, 182, 183], "size": 46, "min_range": 2.6639178842306137, "max_range": 5.399838346987963, "mean_range": 3.7198130507022142, "boundaries": [2.641124312431993, 5.652012192922785]}, {"group_id": 2, "name": "high", "channels": [21, 54, 66, 100, 128, 152, 163, 164, 168, 175], "size": 10, "min_range": 5.6958361081779, "max_range": 8.068533569574356, "mean_range": 6.819978023692966, "boundaries": [5.652012192922785, 8.662900073413578]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 155, 160, 173, 176, 178], "size": 13, "min_range": 8.874107971787453, "max_range": 15.684176981449127, "mean_range": 11.21363101211878, "boundaries": [8.662900073413578, 15.684176981449127]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 17, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 132, 133, 137, 140, 141, 142, 146, 149, 150, 157, 158, 165, 166, 171, 172, 177, 179, 181], "size": 115, "min_range": 0.16614410653710365, "max_range": 2.3530073761940002, "mean_range": 0.7940175679390845, "boundaries": [-0.40059849255186464, 2.427032148445268]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 18, 21, 26, 31, 32, 35, 37, 38, 53, 67, 81, 86, 99, 120, 123, 126, 127, 129, 130, 131, 134, 136, 138, 139, 143, 145, 147, 151, 153, 154, 156, 159, 161, 162, 167, 169, 170, 174, 180, 182, 183], "size": 44, "min_range": 2.4968576431274414, "max_range": 4.774808168411255, "mean_range": 3.323707617141984, "boundaries": [2.427032148445268, 5.254662789442401]}, {"group_id": 2, "name": "high", "channels": [54, 60, 66, 79, 100, 128, 144, 152, 160, 163, 168, 173, 175], "size": 13, "min_range": 5.288540363311768, "max_range": 7.404197454452515, "mean_range": 6.080518988462595, "boundaries": [5.254662789442401, 8.082293430439535]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 155, 164, 176, 178], "size": 12, "min_range": 8.361228942871094, "max_range": 15.534576416015625, "mean_range": 10.831001083056131, "boundaries": [8.082293430439535, 15.534576416015625]}]}]}