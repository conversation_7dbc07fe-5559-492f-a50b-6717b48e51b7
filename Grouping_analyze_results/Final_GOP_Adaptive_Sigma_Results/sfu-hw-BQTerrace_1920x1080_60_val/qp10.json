{"sequence": "sfu-hw-BQTerrace_1920x1080_60_val", "qp": "qp10", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BQTerrace_1920x1080_60_val/qp10/codec_output/sidecar_data/minmax.npy", "n_frames": 129, "n_channels": 186, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 137, 138, 139, 141, 142, 143, 147, 150, 151, 158, 166, 167, 172, 173, 175, 178, 180, 183], "size": 116, "min_range": 0.09399005998543894, "max_range": 2.5718427596183924, "mean_range": 0.8695324556322837, "boundaries": [-0.43831727342024696, 2.6753843060794056]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 24, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 99, 100, 120, 123, 126, 127, 128, 129, 131, 132, 140, 144, 145, 146, 148, 152, 153, 154, 155, 157, 159, 160, 162, 163, 168, 170, 171, 181, 182, 184, 185], "size": 49, "min_range": 2.6981478910964842, "max_range": 5.776155580952763, "mean_range": 3.8760925947372717, "boundaries": [2.6753843060794056, 5.789085885579058]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 83, 86, 135, 136, 156, 174, 176, 177], "size": 11, "min_range": 6.1302577974418035, "max_range": 8.745909303025558, "mean_range": 7.418432464580201, "boundaries": [5.789085885579058, 8.90278746507871]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 149, 161, 164, 165, 169, 179], "size": 10, "min_range": 9.841773124153796, "max_range": 17.75574069630641, "mean_range": 12.522442182491606, "boundaries": [8.90278746507871, 17.75574069630641]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 135, 138, 141, 142, 143, 147, 150, 151, 158, 162, 166, 167, 172, 173, 175, 178, 180, 183], "size": 116, "min_range": 0.056729200267000124, "max_range": 2.665603623725474, "mean_range": 0.8523707204019266, "boundaries": [-0.4925864802506963, 2.6661900806532794]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 24, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 99, 100, 120, 123, 126, 127, 128, 129, 131, 137, 139, 140, 144, 145, 146, 148, 152, 153, 154, 155, 157, 159, 160, 163, 168, 170, 171, 181, 182, 184, 185], "size": 49, "min_range": 2.6846063705161214, "max_range": 5.591648582369089, "mean_range": 3.7893200761885666, "boundaries": [2.6661900806532794, 5.824966641557255]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 83, 86, 132, 156, 174, 176, 177], "size": 10, "min_range": 6.238520409911871, "max_range": 8.355709917843342, "mean_range": 7.534644792787731, "boundaries": [5.824966641557255, 8.98374320246123]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 161, 164, 165, 169, 179], "size": 11, "min_range": 9.877756770700216, "max_range": 17.999161079525948, "mean_range": 12.364838161251761, "boundaries": [8.98374320246123, 17.999161079525948]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 132, 133, 134, 137, 139, 141, 142, 143, 147, 150, 151, 158, 166, 167, 172, 173, 175, 178, 180, 183], "size": 116, "min_range": 0.08692354899472915, "max_range": 2.5736560518925007, "mean_range": 0.8485986393928251, "boundaries": [-0.48034718624922634, 2.684578531505532]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 24, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 99, 100, 120, 123, 126, 127, 128, 129, 136, 138, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 163, 168, 170, 171, 181, 182, 184, 185], "size": 48, "min_range": 2.711689411676847, "max_range": 5.279712612812336, "mean_range": 3.807681574382915, "boundaries": [2.684578531505532, 5.84950424926029]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 83, 86, 131, 153, 156, 174, 177], "size": 10, "min_range": 5.960662579536438, "max_range": 8.492188255603496, "mean_range": 7.37315383049158, "boundaries": [5.84950424926029, 9.01442996701505]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 149, 161, 164, 165, 169, 176, 179], "size": 12, "min_range": 9.136108688207774, "max_range": 17.512320313086878, "mean_range": 12.0328259012638, "boundaries": [9.01442996701505, 17.512320313086878]}]}]}