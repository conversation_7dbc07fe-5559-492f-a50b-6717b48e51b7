{"sequence": "sfu-hw-Traffic_2560x1600_30_val", "qp": "qp16", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data/minmax.npy", "n_frames": 33, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 127, 132, 133, 137, 138, 140, 141, 142, 146, 149, 150, 153, 157, 165, 166, 171, 172, 177, 179], "size": 115, "min_range": 0.15677356265950948, "max_range": 2.5825296360999346, "mean_range": 0.8548518870835718, "boundaries": [-0.3440405362364203, 2.587996208068469]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 37, 53, 60, 79, 81, 99, 100, 112, 120, 123, 126, 128, 129, 130, 134, 136, 139, 143, 144, 145, 151, 154, 156, 158, 159, 161, 162, 167, 169, 173, 174, 180, 181, 182, 183], "size": 46, "min_range": 2.5959792900830507, "max_range": 5.44738064520061, "mean_range": 3.696309480127757, "boundaries": [2.587996208068469, 5.5200329523733584]}, {"group_id": 2, "name": "high", "channels": [49, 54, 86, 131, 147, 152, 155, 163, 164, 170], "size": 10, "min_range": 5.860705668106675, "max_range": 7.567713148891926, "mean_range": 6.485752123594284, "boundaries": [5.5200329523733584, 8.452069696678247]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 66, 83, 93, 104, 110, 135, 148, 160, 168, 175, 176, 178], "size": 13, "min_range": 8.47257748246193, "max_range": 14.538560546934605, "mean_range": 10.999659842166762, "boundaries": [8.452069696678247, 14.538560546934605]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 132, 133, 137, 138, 140, 141, 142, 146, 149, 150, 157, 165, 166, 171, 172, 177, 179], "size": 113, "min_range": 0.15215420932509005, "max_range": 2.53778662532568, "mean_range": 0.831211255302867, "boundaries": [-0.3862583962267103, 2.6130017644280326]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 37, 53, 60, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 129, 130, 134, 136, 139, 143, 144, 145, 151, 152, 153, 154, 156, 158, 159, 161, 162, 167, 169, 173, 174, 180, 181, 182, 183], "size": 49, "min_range": 2.6624130215495825, "max_range": 5.6110458336770535, "mean_range": 3.6892084856802714, "boundaries": [2.6130017644280326, 5.612261925082775]}, {"group_id": 2, "name": "high", "channels": [49, 54, 86, 131, 147, 155, 163, 164, 168, 170], "size": 10, "min_range": 5.891941893845797, "max_range": 8.568945579230785, "mean_range": 6.8297124590724705, "boundaries": [5.612261925082775, 8.61152208573752]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 66, 83, 93, 104, 110, 135, 148, 160, 175, 176, 178], "size": 12, "min_range": 8.845904886722565, "max_range": 14.759908512234688, "mean_range": 11.483092701373002, "boundaries": [8.61152208573752, 14.759908512234688]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 21, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 127, 133, 134, 137, 138, 140, 141, 142, 146, 149, 150, 153, 157, 158, 165, 166, 171, 172, 177, 179], "size": 117, "min_range": 0.1613929159939289, "max_range": 2.5253676772117615, "mean_range": 0.8745747247440183, "boundaries": [-0.3196031669638115, 2.562990651708906]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 24, 26, 31, 32, 35, 37, 53, 60, 79, 81, 99, 100, 112, 120, 123, 126, 128, 129, 130, 132, 136, 139, 143, 144, 145, 151, 154, 156, 159, 161, 162, 167, 169, 173, 174, 180, 181, 182, 183], "size": 44, "min_range": 2.5820173621177673, "max_range": 5.387062191963196, "mean_range": 3.756002763455564, "boundaries": [2.562990651708906, 5.445584470381624]}, {"group_id": 2, "name": "high", "channels": [49, 54, 86, 131, 147, 152, 155, 163, 164, 170, 176], "size": 11, "min_range": 5.533894062042236, "max_range": 8.099250078201294, "mean_range": 6.577185934240168, "boundaries": [5.445584470381624, 8.32817828905434]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 66, 83, 93, 104, 110, 135, 148, 160, 168, 175, 178], "size": 12, "min_range": 8.50532865524292, "max_range": 14.317212581634521, "mean_range": 10.970989187558493, "boundaries": [8.32817828905434, 14.317212581634521]}]}]}