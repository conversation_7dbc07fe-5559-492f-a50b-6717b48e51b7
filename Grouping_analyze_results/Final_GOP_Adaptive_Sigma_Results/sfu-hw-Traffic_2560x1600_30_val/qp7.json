{"sequence": "sfu-hw-Traffic_2560x1600_30_val", "qp": "qp7", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp7/codec_output/sidecar_data/minmax.npy", "n_frames": 33, "n_channels": 188, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 138, 139, 140, 142, 143, 144, 148, 151, 152, 155, 159, 167, 168, 173, 174, 179, 181, 182, 187], "size": 118, "min_range": 0.0, "max_range": 2.4652841947972775, "mean_range": 0.8129822557864951, "boundaries": [-0.3896515314159603, 2.534030697281351]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 37, 53, 60, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 129, 131, 135, 137, 141, 145, 146, 147, 153, 156, 158, 160, 161, 163, 164, 169, 171, 175, 176, 183, 184, 185, 186], "size": 47, "min_range": 2.5825296360999346, "max_range": 5.44738064520061, "mean_range": 3.6726120366378034, "boundaries": [2.534030697281351, 5.457712925978662]}, {"group_id": 2, "name": "high", "channels": [49, 54, 86, 132, 149, 154, 157, 165, 166, 172], "size": 10, "min_range": 5.860705668106675, "max_range": 7.567713148891926, "mean_range": 6.485752123594284, "boundaries": [5.457712925978662, 8.381395154675973]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 66, 83, 93, 104, 110, 136, 150, 162, 170, 177, 178, 180], "size": 13, "min_range": 8.47257748246193, "max_range": 14.538560546934605, "mean_range": 10.999659842166762, "boundaries": [8.381395154675973, 14.538560546934605]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 138, 139, 140, 142, 143, 144, 148, 151, 152, 159, 167, 168, 173, 174, 179, 181, 182, 187], "size": 117, "min_range": 0.0, "max_range": 2.53778662532568, "mean_range": 0.8045264629642906, "boundaries": [-0.43165090498710423, 2.558484303044447]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 37, 53, 60, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 129, 131, 135, 137, 141, 145, 146, 147, 153, 155, 156, 158, 160, 161, 163, 164, 169, 171, 176, 183, 184, 185, 186], "size": 47, "min_range": 2.6624130215495825, "max_range": 5.489130683243275, "mean_range": 3.6077967941206186, "boundaries": [2.558484303044447, 5.548619511075998]}, {"group_id": 2, "name": "high", "channels": [54, 86, 132, 149, 154, 157, 165, 166, 170, 172, 175], "size": 11, "min_range": 5.593720640987158, "max_range": 8.44292315095663, "mean_range": 6.448449589650739, "boundaries": [5.548619511075998, 8.53875471910755]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 66, 83, 93, 104, 110, 136, 150, 162, 177, 178, 180], "size": 13, "min_range": 8.568945579230785, "max_range": 14.759908512234688, "mean_range": 11.258927538131292, "boundaries": [8.53875471910755, 14.759908512234688]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 127, 130, 134, 138, 139, 140, 142, 143, 144, 148, 151, 152, 155, 159, 167, 168, 173, 174, 179, 181, 182, 187], "size": 118, "min_range": 0.0, "max_range": 2.5001180171966553, "mean_range": 0.8048158264837338, "boundaries": [-0.3651086909800396, 2.5095770915182545]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 37, 53, 60, 79, 81, 99, 100, 112, 120, 123, 126, 128, 129, 131, 133, 135, 137, 141, 145, 147, 153, 156, 158, 160, 161, 163, 164, 169, 171, 175, 176, 183, 184, 185, 186], "size": 46, "min_range": 2.5187735557556152, "max_range": 5.321611523628235, "mean_range": 3.640092346979224, "boundaries": [2.5095770915182545, 5.384262874016549]}, {"group_id": 2, "name": "high", "channels": [49, 54, 86, 132, 146, 149, 154, 157, 165, 166, 172, 178], "size": 12, "min_range": 5.387062191963196, "max_range": 8.099250078201294, "mean_range": 6.478008955717087, "boundaries": [5.384262874016549, 8.258948656514843]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 66, 83, 93, 104, 110, 136, 150, 162, 170, 177, 180], "size": 12, "min_range": 8.50532865524292, "max_range": 14.317212581634521, "mean_range": 10.970989187558493, "boundaries": [8.258948656514843, 14.317212581634521]}]}]}