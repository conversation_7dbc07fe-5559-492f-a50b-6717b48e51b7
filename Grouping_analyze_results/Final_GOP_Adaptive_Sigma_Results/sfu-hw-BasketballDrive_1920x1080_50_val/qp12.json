{"sequence": "sfu-hw-BasketballDrive_1920x1080_50_val", "qp": "qp12", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballDrive_1920x1080_50_val/qp12/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 186, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 18, 20, 22, 23, 24, 25, 27, 28, 29, 30, 32, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 138, 141, 142, 143, 147, 151, 158, 159, 166, 167, 172, 173, 178, 180, 183], "size": 115, "min_range": 0.0666347292064915, "max_range": 2.8604246869005943, "mean_range": 0.9089953035120671, "boundaries": [-0.6548146874586851, 2.864770005125545]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 19, 21, 26, 31, 35, 37, 53, 60, 66, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 131, 132, 137, 139, 140, 144, 145, 146, 148, 150, 152, 154, 155, 157, 160, 162, 163, 168, 170, 171, 175, 181, 182, 184, 185], "size": 51, "min_range": 2.900665580972352, "max_range": 6.06602725482574, "mean_range": 3.947536306423083, "boundaries": [2.864770005125545, 6.3843546977097745]}, {"group_id": 2, "name": "high", "channels": [54, 86, 164, 165, 169, 174, 176], "size": 7, "min_range": 6.530781855609155, "max_range": 8.789787900493, "mean_range": 7.732820547593041, "boundaries": [6.3843546977097745, 9.903939390294004]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 153, 156, 161, 177, 179], "size": 13, "min_range": 10.210152896290476, "max_range": 21.9587489706323, "mean_range": 13.296820429902706, "boundaries": [9.903939390294004, 21.9587489706323]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 18, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 131, 133, 134, 138, 141, 142, 143, 147, 151, 158, 166, 167, 172, 173, 178, 180, 183], "size": 115, "min_range": 0.07424291118513793, "max_range": 2.8857565242797136, "mean_range": 0.9240480344260978, "boundaries": [-0.7114773970435699, 2.9174659932385416]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 19, 21, 26, 35, 37, 53, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 132, 135, 137, 139, 140, 144, 145, 146, 148, 150, 152, 154, 155, 157, 159, 160, 162, 163, 168, 170, 171, 175, 181, 182, 184, 185], "size": 52, "min_range": 2.926363382488489, "max_range": 6.247311977669597, "mean_range": 4.042133709182963, "boundaries": [2.9174659932385416, 6.546409383520653]}, {"group_id": 2, "name": "high", "channels": [54, 164, 165, 169, 174, 176], "size": 6, "min_range": 6.855112170800567, "max_range": 9.195738278329372, "mean_range": 7.9404408953463035, "boundaries": [6.546409383520653, 10.175352773802764]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 153, 156, 161, 177, 179], "size": 13, "min_range": 10.315394833683968, "max_range": 23.120151855051517, "mean_range": 13.734580964136581, "boundaries": [10.175352773802764, 23.120151855051517]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 18, 20, 22, 23, 24, 25, 27, 28, 29, 30, 32, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 138, 141, 142, 143, 147, 151, 158, 159, 166, 167, 172, 173, 178, 180, 183], "size": 115, "min_range": 0.05902654722784505, "max_range": 2.7733368133053635, "mean_range": 0.8921848734197477, "boundaries": [-0.6069599707427207, 2.812074017012548]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 19, 21, 26, 31, 35, 37, 53, 60, 66, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 131, 132, 137, 139, 140, 144, 145, 146, 148, 150, 152, 154, 155, 157, 160, 162, 163, 168, 170, 171, 175, 181, 182, 184, 185], "size": 51, "min_range": 2.8542455597357317, "max_range": 6.204355297666607, "mean_range": 3.9001411325649644, "boundaries": [2.812074017012548, 6.231108004767817]}, {"group_id": 2, "name": "high", "channels": [54, 86, 164, 165, 169, 174, 176], "size": 7, "min_range": 6.561775518186165, "max_range": 9.081099698037812, "mean_range": 7.767075759507876, "boundaries": [6.231108004767817, 9.650141992523086]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 153, 156, 161, 177, 179], "size": 13, "min_range": 10.055021950692842, "max_range": 20.797346086213082, "mean_range": 12.859059895668828, "boundaries": [9.650141992523086, 20.797346086213082]}]}]}