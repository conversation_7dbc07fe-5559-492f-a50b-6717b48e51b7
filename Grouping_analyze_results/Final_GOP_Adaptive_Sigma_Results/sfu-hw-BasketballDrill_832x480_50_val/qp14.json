{"sequence": "sfu-hw-BasketballDrill_832x480_50_val", "qp": "qp14", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballDrill_832x480_50_val/qp14/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 130, 132, 133, 134, 137, 140, 141, 142, 146, 149, 150, 157, 166, 171, 172, 177, 179], "size": 112, "min_range": 0.20063867771673496, "max_range": 3.169176005490237, "mean_range": 0.9642857712570289, "boundaries": [-0.7168055409051881, 3.2142669407929056]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 54, 60, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 131, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 156, 158, 159, 161, 162, 165, 167, 169, 170, 174, 180, 181, 182, 183], "size": 52, "min_range": 3.233513819420654, "max_range": 7.074329295195639, "mean_range": 4.486427207254601, "boundaries": [3.2142669407929056, 7.145339422490999]}, {"group_id": 2, "name": "high", "channels": [66, 83, 86, 152, 168, 173, 175, 176], "size": 8, "min_range": 7.376566000282764, "max_range": 11.02982877872207, "mean_range": 8.751546091484752, "boundaries": [7.145339422490999, 11.076411904189094]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 93, 104, 110, 135, 148, 155, 160, 163, 164, 178], "size": 12, "min_range": 11.644226498393849, "max_range": 25.011372004494522, "mean_range": 15.00987726799918, "boundaries": [11.076411904189094, 25.011372004494522]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 130, 132, 133, 134, 137, 140, 141, 142, 146, 149, 150, 156, 157, 166, 171, 172, 177, 179], "size": 113, "min_range": 0.19800619152374566, "max_range": 3.2617171611636877, "mean_range": 0.9938107533983372, "boundaries": [-0.7240920140943161, 3.2696778753170874]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 131, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 158, 159, 161, 162, 165, 167, 169, 170, 174, 180, 181, 182, 183], "size": 50, "min_range": 3.3103731870651245, "max_range": 6.722736518830061, "mean_range": 4.56830724498257, "boundaries": [3.2696778753170874, 7.263447764728491]}, {"group_id": 2, "name": "high", "channels": [54, 66, 83, 86, 152, 168, 173, 175, 176], "size": 9, "min_range": 7.403592720627785, "max_range": 10.740656793117523, "mean_range": 8.640067231530944, "boundaries": [7.263447764728491, 11.257217654139895]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 93, 104, 110, 135, 148, 155, 160, 163, 164, 178], "size": 12, "min_range": 11.994492541998625, "max_range": 25.49940311908722, "mean_range": 15.262012215952078, "boundaries": [11.257217654139895, 25.49940311908722]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 129, 132, 133, 134, 137, 140, 141, 142, 146, 149, 150, 157, 166, 171, 172, 177, 179], "size": 113, "min_range": 0.20327116390972427, "max_range": 3.0539402130878335, "mean_range": 0.9711646200052961, "boundaries": [-0.7140833124091048, 3.158856006268724]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 32, 35, 37, 53, 54, 60, 67, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 130, 131, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 156, 158, 159, 161, 162, 165, 167, 169, 170, 174, 180, 181, 182], "size": 50, "min_range": 3.163180896730134, "max_range": 6.721434593200684, "mean_range": 4.3530371305436795, "boundaries": [3.158856006268724, 7.031795324946552]}, {"group_id": 2, "name": "high", "channels": [66, 86, 152, 168, 173, 175, 176, 183], "size": 8, "min_range": 7.051782347939231, "max_range": 10.170773780707156, "mean_range": 8.178017245097593, "boundaries": [7.031795324946552, 10.904734643624382]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 155, 160, 163, 164, 178], "size": 13, "min_range": 11.094251784411343, "max_range": 24.523340889901824, "mean_range": 14.493223738837075, "boundaries": [10.904734643624382, 24.523340889901824]}]}]}