{"sequence": "sfu-hw-BQMall_832x480_60_val", "qp": "qp5", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BQMall_832x480_60_val/qp5/codec_output/sidecar_data/minmax.npy", "n_frames": 129, "n_channels": 188, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 158, 164, 167, 168, 174, 179, 181, 187], "size": 115, "min_range": 0.0, "max_range": 3.3989636863510198, "mean_range": 0.997356569185712, "boundaries": [-0.6676498983857488, 3.4321820133640797]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 17, 18, 21, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 99, 112, 120, 121, 123, 126, 127, 128, 129, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 163, 169, 171, 172, 173, 176, 182, 183, 184, 185], "size": 49, "min_range": 3.451914359414234, "max_range": 7.41680637047841, "mean_range": 4.795191794380201, "boundaries": [3.4321820133640797, 7.532013925113908]}, {"group_id": 2, "name": "high", "channels": [16, 49, 54, 66, 83, 86, 100, 153, 165, 166, 170, 175, 177, 178, 186], "size": 15, "min_range": 7.658624426619365, "max_range": 11.578537599283916, "mean_range": 9.878592020970506, "boundaries": [7.532013925113908, 11.631845836863736]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 156, 161, 180], "size": 9, "min_range": 13.300781116634607, "max_range": 24.239258600656804, "mean_range": 16.37899275743363, "boundaries": [11.631845836863736, 24.239258600656804]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 158, 164, 167, 168, 174, 179, 181, 187], "size": 114, "min_range": 0.0, "max_range": 3.4325661677867174, "mean_range": 0.9917539925510475, "boundaries": [-0.7283516368522229, 3.546830373479326]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 169, 171, 172, 173, 176, 182, 183, 184, 185], "size": 52, "min_range": 3.550280259922147, "max_range": 7.783402692526579, "mean_range": 5.062870732060849, "boundaries": [3.546830373479326, 7.8220123838108755]}, {"group_id": 2, "name": "high", "channels": [16, 49, 54, 83, 153, 163, 166, 170, 175, 177, 178, 186], "size": 12, "min_range": 7.92700469493866, "max_range": 12.07361213862896, "mean_range": 10.351316103090843, "boundaries": [7.8220123838108755, 12.097194394142424]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 156, 161, 165, 180], "size": 10, "min_range": 12.255723007023335, "max_range": 25.154401242733, "mean_range": 16.625908375903965, "boundaries": [12.097194394142424, 25.154401242733]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 158, 164, 167, 168, 174, 176, 179, 181, 187], "size": 117, "min_range": 0.0, "max_range": 3.2942718725938063, "mean_range": 1.0170445253645155, "boundaries": [-0.6285351589839867, 3.3175336532488324]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 17, 18, 21, 26, 32, 35, 37, 53, 60, 67, 79, 81, 99, 112, 120, 121, 123, 126, 127, 128, 129, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 163, 169, 171, 172, 173, 182, 183, 184, 185], "size": 47, "min_range": 3.3535484589063205, "max_range": 6.90660804601816, "mean_range": 4.658552566078018, "boundaries": [3.3175336532488324, 7.263602465481652]}, {"group_id": 2, "name": "high", "channels": [16, 49, 54, 66, 83, 86, 100, 165, 166, 170, 175, 177, 186], "size": 13, "min_range": 7.469551886045016, "max_range": 11.19767453120305, "mean_range": 9.331985559689222, "boundaries": [7.263602465481652, 11.20967127771447]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 153, 156, 161, 178, 180], "size": 11, "min_range": 11.506942415237427, "max_range": 23.324115958580602, "mean_range": 14.948575860136874, "boundaries": [11.20967127771447, 23.324115958580602]}]}]}