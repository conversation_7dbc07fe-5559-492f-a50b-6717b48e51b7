{"sequence": "sfu-hw-BQMall_832x480_60_val", "qp": "qp22", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BQMall_832x480_60_val/qp22/codec_output/sidecar_data/minmax.npy", "n_frames": 129, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 164, 165, 168, 171, 176, 183], "size": 114, "min_range": 0.0, "max_range": 3.4897469046454015, "mean_range": 1.0936657400652272, "boundaries": [-0.610156162168717, 3.504352670745425]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 17, 18, 21, 26, 32, 35, 37, 53, 60, 67, 79, 81, 99, 112, 120, 121, 123, 126, 127, 128, 129, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 157, 158, 160, 161, 166, 169, 170, 173, 178, 179, 180, 181], "size": 46, "min_range": 3.574543670693842, "max_range": 7.41680637047841, "mean_range": 4.881156128657869, "boundaries": [3.504352670745425, 7.618861503659566]}, {"group_id": 2, "name": "high", "channels": [16, 49, 54, 66, 83, 86, 100, 151, 162, 163, 167, 172, 174, 175, 182], "size": 15, "min_range": 7.658624426619365, "max_range": 11.578537599283916, "mean_range": 9.878592020970506, "boundaries": [7.618861503659566, 11.733370336573708]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 154, 159, 177], "size": 9, "min_range": 13.300781116634607, "max_range": 24.239258600656804, "mean_range": 16.37899275743363, "boundaries": [11.733370336573708, 24.239258600656804]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 164, 165, 168, 171, 176, 183], "size": 112, "min_range": 0.0, "max_range": 3.5771414395421743, "mean_range": 1.069075774369456, "boundaries": [-0.6694972819167582, 3.6214848918109164]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 157, 158, 160, 166, 169, 170, 173, 178, 179, 180, 181], "size": 50, "min_range": 3.6478597205132246, "max_range": 7.783402692526579, "mean_range": 5.122837127353996, "boundaries": [3.6214848918109164, 7.912467065538591]}, {"group_id": 2, "name": "high", "channels": [16, 49, 54, 83, 151, 161, 163, 167, 172, 174, 175, 182], "size": 12, "min_range": 7.92700469493866, "max_range": 12.07361213862896, "mean_range": 10.351316103090843, "boundaries": [7.912467065538591, 12.203449239266266]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 154, 159, 162, 177], "size": 10, "min_range": 12.255723007023335, "max_range": 25.154401242733, "mean_range": 16.625908375903965, "boundaries": [12.203449239266266, 25.154401242733]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 164, 165, 171, 173, 176, 183], "size": 115, "min_range": 0.0, "max_range": 3.3545906488712016, "mean_range": 1.089170300050025, "boundaries": [-0.5727902951015786, 3.387220449679933]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 17, 18, 21, 32, 35, 37, 53, 60, 67, 79, 81, 99, 112, 120, 121, 123, 126, 127, 128, 129, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 157, 158, 160, 161, 166, 168, 169, 170, 178, 179, 180, 181], "size": 45, "min_range": 3.402352369748629, "max_range": 6.90660804601816, "mean_range": 4.7165295888419845, "boundaries": [3.387220449679933, 7.347231194461445]}, {"group_id": 2, "name": "high", "channels": [16, 49, 54, 66, 83, 86, 100, 162, 163, 167, 172, 174, 182], "size": 13, "min_range": 7.469551886045016, "max_range": 11.19767453120305, "mean_range": 9.331985559689222, "boundaries": [7.347231194461445, 11.307241939242957]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 151, 154, 159, 175, 177], "size": 11, "min_range": 11.506942415237427, "max_range": 23.324115958580602, "mean_range": 14.948575860136874, "boundaries": [11.307241939242957, 23.324115958580602]}]}]}