{"sequence": "sfu-hw-BQMall_832x480_60_val", "qp": "qp24", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BQMall_832x480_60_val/qp24/codec_output/sidecar_data/minmax.npy", "n_frames": 129, "n_channels": 154, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 41, 43, 44, 45, 46, 47, 48, 50, 51, 53, 54, 55, 56, 57, 58, 60, 62, 66, 67, 68, 69, 72, 73, 74, 76, 78, 80, 82, 83, 84, 86, 87, 88, 89, 90, 91, 92, 94, 95, 97, 101, 104, 105, 107, 108, 111, 112, 113, 117, 120, 123, 126, 127, 128, 129, 135, 136, 139, 141, 142, 144, 147, 149], "size": 94, "min_range": 0.600007579746083, "max_range": 4.05038182041966, "mean_range": 1.9424821701156583, "boundaries": [0.3376809322723422, 4.091236301067549]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 22, 24, 36, 37, 40, 42, 49, 52, 59, 61, 63, 64, 65, 75, 77, 93, 96, 98, 99, 100, 102, 103, 109, 110, 114, 115, 116, 118, 121, 124, 131, 132, 137, 140, 150, 151, 152], "size": 41, "min_range": 4.15013771085785, "max_range": 7.41680637047841, "mean_range": 5.2238242702539415, "boundaries": [4.091236301067549, 7.844791669862756]}, {"group_id": 2, "name": "high", "channels": [70, 71, 79, 81, 122, 133, 134, 138, 143, 145, 146, 153], "size": 12, "min_range": 8.488510848180606, "max_range": 11.484132951727279, "mean_range": 10.169064892201314, "boundaries": [7.844791669862756, 11.598347038657963]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 85, 106, 119, 125, 130, 148], "size": 7, "min_range": 13.300781116634607, "max_range": 24.239258600656804, "mean_range": 15.893070369529037, "boundaries": [11.598347038657963, 24.239258600656804]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 43, 44, 45, 46, 48, 49, 50, 51, 54, 55, 56, 57, 58, 59, 60, 63, 65, 67, 68, 69, 70, 72, 73, 74, 75, 78, 79, 80, 82, 83, 84, 86, 87, 88, 89, 90, 91, 92, 94, 95, 97, 101, 104, 105, 107, 108, 111, 112, 113, 117, 120, 123, 126, 127, 129, 135, 136, 139, 141, 142, 144, 147, 149, 152], "size": 96, "min_range": 0.5461415678728372, "max_range": 4.121544159948826, "mean_range": 1.6624556065380507, "boundaries": [-0.21127889949518242, 4.230028037757036]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 10, 24, 26, 41, 47, 52, 53, 61, 62, 66, 76, 77, 93, 96, 98, 99, 100, 102, 103, 109, 110, 114, 115, 116, 118, 121, 124, 128, 131, 132, 137, 140, 150, 151], "size": 38, "min_range": 4.252325410023332, "max_range": 8.121658407151699, "mean_range": 5.721114886655031, "boundaries": [4.230028037757036, 8.671334975009254]}, {"group_id": 2, "name": "high", "channels": [37, 42, 64, 85, 122, 133, 134, 138, 143, 145, 146, 153], "size": 12, "min_range": 8.904683828353882, "max_range": 13.098103255033493, "mean_range": 11.126746366421381, "boundaries": [8.671334975009254, 13.112641912261472]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 71, 81, 106, 119, 125, 130, 148], "size": 8, "min_range": 14.086415067315102, "max_range": 25.154401242733, "mean_range": 17.61315718712285, "boundaries": [13.112641912261472, 25.154401242733]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 41, 42, 43, 44, 46, 47, 48, 51, 52, 53, 54, 55, 56, 57, 58, 60, 62, 64, 66, 67, 68, 69, 71, 72, 73, 76, 77, 78, 80, 81, 82, 83, 84, 86, 88, 89, 90, 91, 92, 94, 95, 97, 98, 101, 103, 104, 105, 107, 108, 111, 112, 113, 117, 120, 123, 126, 127, 128, 129, 135, 136, 137, 139, 141, 142, 144, 147, 149, 151], "size": 99, "min_range": 0.488505497116309, "max_range": 3.870267515916091, "mean_range": 1.6472637920211295, "boundaries": [-0.11555851722806132, 3.9524445643780615]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 7, 8, 9, 22, 24, 39, 45, 50, 59, 61, 65, 74, 87, 93, 96, 99, 100, 102, 109, 110, 114, 115, 116, 118, 121, 124, 131, 132, 140, 150, 152], "size": 33, "min_range": 3.961894490168645, "max_range": 7.8251854882790495, "mean_range": 5.348485601420725, "boundaries": [3.9524445643780615, 8.020447645984184]}, {"group_id": 2, "name": "high", "channels": [36, 40, 49, 63, 75, 106, 122, 133, 134, 138, 143, 145, 146, 153], "size": 14, "min_range": 8.14228735749538, "max_range": 11.8083068554218, "mean_range": 10.029504938833009, "boundaries": [8.020447645984184, 12.088450727590306]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 70, 79, 85, 119, 125, 130, 148], "size": 8, "min_range": 12.763590060747587, "max_range": 23.324115958580602, "mean_range": 16.08553168919797, "boundaries": [12.088450727590306, 23.324115958580602]}]}]}