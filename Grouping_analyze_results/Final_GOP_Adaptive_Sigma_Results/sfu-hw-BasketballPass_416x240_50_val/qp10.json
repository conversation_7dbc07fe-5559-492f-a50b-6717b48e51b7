{"sequence": "sfu-hw-BasketballPass_416x240_50_val", "qp": "qp10", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp10/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 186, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 157, 158, 160, 166, 167, 172, 173, 178, 180], "size": 116, "min_range": 0.0782687646168404, "max_range": 3.0693071975485617, "mean_range": 0.9719123563896684, "boundaries": [-0.6915211069093359, 3.088914978374926]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 159, 162, 163, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 50, "min_range": 3.374137189577926, "max_range": 6.404933271449849, "mean_range": 4.4416184794674205, "boundaries": [3.088914978374926, 6.869351063659188]}, {"group_id": 2, "name": "high", "channels": [54, 86, 153, 161, 164, 165, 169, 174, 176, 177], "size": 10, "min_range": 6.969527026412614, "max_range": 10.601386159263326, "mean_range": 8.963611755680969, "boundaries": [6.869351063659188, 10.64978714894345]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 156, 179], "size": 10, "min_range": 10.88495511990605, "max_range": 23.657893865058817, "mean_range": 15.0079311106354, "boundaries": [10.64978714894345, 23.657893865058817]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 157, 158, 160, 166, 167, 173, 178, 180], "size": 115, "min_range": 0.08054274172172882, "max_range": 3.1950712436810136, "mean_range": 0.9912960666759337, "boundaries": [-0.6602179527203851, 3.1985757398239523]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 159, 162, 163, 168, 170, 171, 172, 175, 181, 182, 183, 184, 185], "size": 52, "min_range": 3.2155539905652404, "max_range": 7.005540754646063, "mean_range": 4.66782134029871, "boundaries": [3.1985757398239523, 7.05736943236829]}, {"group_id": 2, "name": "high", "channels": [54, 83, 153, 165, 169, 174, 176, 177], "size": 8, "min_range": 7.129345467314124, "max_range": 10.885418117046356, "mean_range": 9.32404332025908, "boundaries": [7.05736943236829, 10.916163124912627]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 93, 104, 110, 136, 149, 156, 161, 164, 179], "size": 11, "min_range": 10.964417684823275, "max_range": 23.63938818126917, "mean_range": 14.874271243810654, "boundaries": [10.916163124912627, 23.63938818126917]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 157, 158, 160, 166, 167, 172, 173, 178, 180], "size": 116, "min_range": 0.07599478751195199, "max_range": 2.9230604045318835, "mean_range": 0.9333540088284952, "boundaries": [-0.7313237059164632, 2.9792542169259]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 159, 162, 163, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 50, "min_range": 2.986515381119468, "max_range": 6.484462694688276, "mean_range": 4.233124659928409, "boundaries": [2.9792542169259, 6.689832139768264]}, {"group_id": 2, "name": "high", "channels": [54, 86, 153, 161, 164, 169, 174, 176, 177], "size": 9, "min_range": 6.9335132981791645, "max_range": 10.148527759494204, "mean_range": 8.419691760531983, "boundaries": [6.689832139768264, 10.400410062610627]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 149, 156, 165, 179], "size": 11, "min_range": 10.478131258126462, "max_range": 23.67639954884847, "mean_range": 14.403523680263971, "boundaries": [10.400410062610627, 23.67639954884847]}]}]}