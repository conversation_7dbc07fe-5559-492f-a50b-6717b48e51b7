{"sequence": "sfu-hw-BasketballPass_416x240_50_val", "qp": "qp24", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 164, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "low", "channels": [163], "size": 1, "min_range": 0.0, "max_range": 0.0, "mean_range": 0.0, "boundaries": [-3.4143503095452403, 0.01602039613824857]}, {"group_id": 1, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 43, 45, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 59, 60, 61, 62, 63, 64, 65, 66, 68, 71, 73, 74, 75, 76, 79, 80, 81, 82, 85, 86, 89, 90, 91, 94, 95, 96, 97, 98, 99, 100, 103, 105, 109, 112, 113, 114, 116, 117, 120, 121, 122, 126, 129, 135, 136, 138, 144, 145, 149, 150, 151, 156, 158], "size": 99, "min_range": 0.5103160112736408, "max_range": 3.4407174348774734, "mean_range": 1.5180496144666609, "boundaries": [0.01602039613824857, 3.4463911018217375]}, {"group_id": 2, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 40, 42, 44, 46, 47, 58, 67, 69, 70, 83, 84, 92, 101, 102, 104, 106, 107, 108, 110, 111, 118, 119, 123, 124, 125, 127, 130, 132, 133, 137, 140, 141, 146, 148, 153, 159, 160, 161, 162], "size": 45, "min_range": 3.476088041298543, "max_range": 6.404933271449849, "mean_range": 4.588797379737353, "boundaries": [3.4463911018217375, 6.876761807505226]}, {"group_id": 3, "name": "high", "channels": [72, 87, 88, 93, 131, 147, 152, 154, 155], "size": 9, "min_range": 6.969527026412614, "max_range": 9.791152949416727, "mean_range": 8.259108973391077, "boundaries": [6.876761807505226, 10.307132513188716]}, {"group_id": 4, "name": "ultra_high", "channels": [6, 77, 78, 115, 128, 134, 139, 142, 143, 157], "size": 10, "min_range": 10.507317999942284, "max_range": 23.657893865058817, "mean_range": 13.409336601786501, "boundaries": [10.307132513188716, 23.657893865058817]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 43, 44, 45, 48, 49, 50, 51, 52, 54, 55, 56, 57, 60, 61, 62, 63, 64, 65, 66, 69, 71, 73, 74, 75, 76, 77, 79, 80, 81, 82, 85, 86, 87, 89, 90, 91, 92, 95, 96, 97, 98, 99, 100, 103, 105, 109, 110, 112, 113, 114, 116, 117, 120, 121, 122, 126, 129, 135, 136, 138, 144, 145, 148, 150, 151, 156, 163], "size": 98, "min_range": 0.0, "max_range": 3.544867386110127, "mean_range": 1.2386742009214209, "boundaries": [-0.39656344597630566, 3.569054229163591]}, {"group_id": 1, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 19, 25, 28, 30, 46, 47, 53, 58, 59, 67, 68, 72, 83, 84, 94, 101, 102, 104, 106, 107, 108, 111, 118, 119, 123, 124, 125, 127, 130, 132, 133, 137, 140, 141, 146, 149, 153, 158, 159, 160, 161, 162], "size": 48, "min_range": 3.580064093694091, "max_range": 7.129345467314124, "mean_range": 4.851142439913626, "boundaries": [3.569054229163591, 7.534671904303488]}, {"group_id": 2, "name": "high", "channels": [70, 131, 139, 142, 143, 147, 152, 154, 155], "size": 9, "min_range": 8.215736631304026, "max_range": 11.339991126209497, "mean_range": 9.974156656199032, "boundaries": [7.534671904303488, 11.500289579443384]}, {"group_id": 3, "name": "ultra_high", "channels": [6, 42, 78, 88, 93, 115, 128, 134, 157], "size": 9, "min_range": 12.046746753156185, "max_range": 23.63938818126917, "mean_range": 15.701397207876047, "boundaries": [11.500289579443384, 23.63938818126917]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 13, 15, 16, 17, 19, 20, 21, 22, 23, 24, 25, 26, 28, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 45, 46, 47, 48, 49, 51, 52, 53, 54, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 70, 71, 73, 74, 75, 76, 78, 79, 80, 81, 84, 85, 86, 88, 89, 90, 91, 93, 94, 95, 96, 97, 98, 99, 100, 103, 105, 109, 112, 113, 114, 116, 117, 120, 121, 122, 126, 129, 132, 135, 136, 138, 144, 145, 149, 150, 151, 156, 158, 163], "size": 101, "min_range": 0.0, "max_range": 3.301370776060856, "mean_range": 1.203768310955528, "boundaries": [-0.498688223716532, 3.3237279744798847]}, {"group_id": 1, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 12, 14, 18, 27, 29, 43, 44, 50, 55, 66, 67, 72, 82, 83, 101, 102, 104, 106, 107, 108, 110, 111, 118, 119, 123, 124, 125, 127, 130, 133, 137, 140, 141, 146, 148, 153, 159, 160, 161, 162], "size": 45, "min_range": 3.342740692875602, "max_range": 6.9389953504909165, "mean_range": 4.5148291273149175, "boundaries": [3.3237279744798847, 7.146144172676301]}, {"group_id": 2, "name": "high", "channels": [69, 92, 131, 139, 142, 143, 147, 152, 154, 155], "size": 10, "min_range": 7.557617436755788, "max_range": 10.884492122765744, "mean_range": 9.378914202704575, "boundaries": [7.146144172676301, 10.968560370872718]}, {"group_id": 3, "name": "ultra_high", "channels": [6, 40, 77, 87, 115, 128, 134, 157], "size": 8, "min_range": 11.062973557096539, "max_range": 23.67639954884847, "mean_range": 15.819291956496961, "boundaries": [10.968560370872718, 23.67639954884847]}]}]}