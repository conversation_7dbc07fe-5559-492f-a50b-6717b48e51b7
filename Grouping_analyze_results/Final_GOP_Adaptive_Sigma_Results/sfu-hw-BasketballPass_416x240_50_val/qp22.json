{"sequence": "sfu-hw-BasketballPass_416x240_50_val", "qp": "qp22", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp22/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 158, 164, 165, 170, 171, 176, 183], "size": 114, "min_range": 0.0, "max_range": 3.0693071975485617, "mean_range": 0.985784930823362, "boundaries": [-0.6681772893776068, 3.120520841034772]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 157, 160, 161, 166, 168, 169, 173, 178, 179, 180, 181, 182], "size": 50, "min_range": 3.374137189577926, "max_range": 6.404933271449849, "mean_range": 4.4416184794674205, "boundaries": [3.120520841034772, 6.909218971447151]}, {"group_id": 2, "name": "high", "channels": [54, 86, 151, 159, 162, 163, 167, 172, 174, 175], "size": 10, "min_range": 6.969527026412614, "max_range": 10.601386159263326, "mean_range": 8.963611755680969, "boundaries": [6.909218971447151, 10.69791710185953]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 154, 177], "size": 10, "min_range": 10.88495511990605, "max_range": 23.657893865058817, "mean_range": 15.0079311106354, "boundaries": [10.69791710185953, 23.657893865058817]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 158, 164, 165, 170, 171, 176, 183], "size": 114, "min_range": 0.0, "max_range": 3.2155539905652404, "mean_range": 1.024833633720768, "boundaries": [-0.6356401553987623, 3.231258261919158]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 157, 160, 161, 166, 168, 169, 173, 178, 179, 180, 181, 182], "size": 51, "min_range": 3.3469958947971463, "max_range": 7.005540754646063, "mean_range": 4.696297170685641, "boundaries": [3.231258261919158, 7.0981566792370785]}, {"group_id": 2, "name": "high", "channels": [54, 83, 151, 162, 163, 167, 172, 174, 175], "size": 9, "min_range": 7.129345467314124, "max_range": 10.964417684823275, "mean_range": 9.50630713854399, "boundaries": [7.0981566792370785, 10.965055096554998]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 93, 104, 110, 135, 148, 154, 159, 177], "size": 10, "min_range": 11.339991126209497, "max_range": 23.63938818126917, "mean_range": 15.265256599709392, "boundaries": [10.965055096554998, 23.63938818126917]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 32, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 158, 164, 165, 170, 171, 176, 183], "size": 115, "min_range": 0.0, "max_range": 2.986515381119468, "mean_range": 0.9644734379537256, "boundaries": [-0.7092823140259457, 3.0097834201503857]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 35, 37, 53, 60, 66, 67, 79, 81, 99, 100, 112, 120, 121, 123, 126, 127, 128, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 157, 160, 161, 166, 168, 169, 173, 178, 179, 180, 181, 182], "size": 49, "min_range": 3.1217479886430684, "max_range": 6.484462694688276, "mean_range": 4.258565665618387, "boundaries": [3.0097834201503857, 6.728849154326717]}, {"group_id": 2, "name": "high", "channels": [54, 86, 151, 159, 162, 167, 172, 174, 175], "size": 9, "min_range": 6.9335132981791645, "max_range": 10.148527759494204, "mean_range": 8.419691760531983, "boundaries": [6.728849154326717, 10.447914888503048]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 154, 163, 177], "size": 11, "min_range": 10.478131258126462, "max_range": 23.67639954884847, "mean_range": 14.403523680263971, "boundaries": [10.447914888503048, 23.67639954884847]}]}]}