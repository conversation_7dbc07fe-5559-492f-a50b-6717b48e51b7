{"sequence": "sfu-hw-<PERSON><PERSON>_1920x1080_24_val", "qp": "qp21", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-<PERSON><PERSON>_1920x1080_24_val/qp21/codec_output/sidecar_data/minmax.npy", "n_frames": 33, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 26, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 132, 133, 134, 137, 139, 140, 141, 142, 146, 155, 156, 158, 165, 170, 171, 176, 178, 180, 183], "size": 116, "min_range": 0.0, "max_range": 2.947008895687759, "mean_range": 0.9570778432319238, "boundaries": [-0.611477178108506, 2.9588004975398094]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 32, 35, 37, 53, 54, 60, 66, 67, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 129, 130, 136, 138, 143, 144, 145, 147, 149, 152, 153, 157, 160, 161, 164, 166, 168, 169, 173, 179, 181, 182], "size": 47, "min_range": 2.9956892635673285, "max_range": 6.290396274998784, "mean_range": 4.224456148241904, "boundaries": [2.9588004975398094, 6.529078173188125]}, {"group_id": 2, "name": "high", "channels": [49, 83, 86, 131, 150, 151, 167, 174, 175], "size": 9, "min_range": 6.537054877728224, "max_range": 8.998523402959108, "mean_range": 7.942613035026524, "boundaries": [6.529078173188125, 10.09935584883644]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 154, 159, 162, 163, 172, 177], "size": 12, "min_range": 10.442142147570848, "max_range": 21.191170632839203, "mean_range": 13.613775454151133, "boundaries": [10.09935584883644, 21.191170632839203]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 18, 19, 20, 22, 23, 25, 26, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 132, 133, 134, 137, 140, 141, 142, 146, 155, 156, 158, 165, 170, 171, 176, 178, 183], "size": 116, "min_range": 0.0, "max_range": 2.9652856439352036, "mean_range": 0.9570215931292704, "boundaries": [-0.611104283182696, 2.973938958322675]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 21, 24, 32, 35, 37, 53, 54, 60, 66, 67, 79, 81, 99, 100, 120, 123, 126, 127, 128, 129, 130, 136, 138, 139, 143, 144, 145, 147, 149, 152, 153, 157, 160, 161, 164, 166, 168, 169, 173, 179, 180, 181, 182], "size": 47, "min_range": 3.015087904408574, "max_range": 6.395264629274607, "mean_range": 4.297539678739106, "boundaries": [2.973938958322675, 6.558982199828046]}, {"group_id": 2, "name": "high", "channels": [49, 83, 86, 131, 150, 151, 167, 174, 175], "size": 9, "min_range": 6.729771576821804, "max_range": 9.317206479609013, "mean_range": 7.952344635294543, "boundaries": [6.558982199828046, 10.144025441333417]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 154, 159, 162, 163, 172, 177], "size": 12, "min_range": 10.325643055140972, "max_range": 21.507458567619324, "mean_range": 13.552899742498994, "boundaries": [10.144025441333417, 21.507458567619324]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 132, 133, 134, 137, 139, 140, 141, 142, 146, 155, 156, 165, 170, 171, 176, 178, 180, 183], "size": 115, "min_range": 0.0, "max_range": 2.892458915710449, "mean_range": 0.9317137107577013, "boundaries": [-0.6249184863655244, 2.9436620367569444]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 31, 32, 35, 37, 53, 54, 60, 66, 67, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 129, 130, 136, 138, 143, 144, 145, 147, 149, 152, 153, 157, 158, 160, 161, 164, 166, 168, 169, 173, 174, 179, 181, 182], "size": 49, "min_range": 2.9519771337509155, "max_range": 6.3443381786346436, "mean_range": 4.190598557190019, "boundaries": [2.9436620367569444, 6.512242559879413]}, {"group_id": 2, "name": "high", "channels": [49, 83, 86, 131, 150, 151, 167, 175], "size": 8, "min_range": 7.022891044616699, "max_range": 9.85102367401123, "mean_range": 8.131449341773987, "boundaries": [6.512242559879413, 10.080823083001881]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 154, 159, 162, 163, 172, 177], "size": 12, "min_range": 10.243053674697876, "max_range": 20.874882698059082, "mean_range": 13.674651165803274, "boundaries": [10.080823083001881, 20.874882698059082]}]}]}