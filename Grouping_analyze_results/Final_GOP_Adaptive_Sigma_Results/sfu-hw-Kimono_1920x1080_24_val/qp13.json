{"sequence": "sfu-hw-<PERSON><PERSON>_1920x1080_24_val", "qp": "qp13", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-<PERSON><PERSON>_1920x1080_24_val/qp13/codec_output/sidecar_data/minmax.npy", "n_frames": 33, "n_channels": 186, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 135, 138, 140, 141, 142, 143, 147, 151, 157, 158, 160, 167, 172, 173, 178, 180, 181], "size": 116, "min_range": 0.08238339371746406, "max_range": 2.909831284545362, "mean_range": 0.9093521349232419, "boundaries": [-0.6338374719870248, 2.9288117771990776]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 32, 35, 37, 53, 54, 60, 66, 67, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 129, 131, 137, 139, 144, 145, 146, 148, 150, 154, 155, 159, 162, 163, 166, 168, 170, 171, 175, 182, 183, 184, 185], "size": 49, "min_range": 2.9288722798228264, "max_range": 6.290396274998784, "mean_range": 4.171945309038369, "boundaries": [2.9288117771990776, 6.4914610263851795]}, {"group_id": 2, "name": "high", "channels": [49, 83, 86, 132, 152, 153, 169, 176, 177], "size": 9, "min_range": 6.537054877728224, "max_range": 8.998523402959108, "mean_range": 7.942613035026524, "boundaries": [6.4914610263851795, 10.054110275571283]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 156, 161, 164, 165, 174, 179], "size": 12, "min_range": 10.442142147570848, "max_range": 21.191170632839203, "mean_range": 13.613775454151133, "boundaries": [10.054110275571283, 21.191170632839203]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 18, 19, 20, 22, 23, 25, 27, 28, 29, 30, 31, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 135, 138, 141, 142, 143, 147, 151, 157, 158, 160, 167, 172, 173, 178, 180], "size": 116, "min_range": 0.08751163340639323, "max_range": 2.848905820399523, "mean_range": 0.9089956032152384, "boundaries": [-0.6336015340606362, 2.9438008254479056]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 21, 24, 26, 32, 35, 37, 53, 54, 60, 66, 67, 79, 81, 99, 100, 120, 123, 126, 127, 128, 129, 131, 137, 139, 140, 144, 145, 146, 148, 150, 154, 155, 159, 162, 163, 166, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 49, "min_range": 2.9479143880307674, "max_range": 6.395264629274607, "mean_range": 4.242807447606204, "boundaries": [2.9438008254479056, 6.521203184956447]}, {"group_id": 2, "name": "high", "channels": [49, 83, 86, 132, 152, 153, 169, 176, 177], "size": 9, "min_range": 6.729771576821804, "max_range": 9.317206479609013, "mean_range": 7.952344635294543, "boundaries": [6.521203184956447, 10.09860554446499]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 156, 161, 164, 165, 174, 179], "size": 12, "min_range": 10.325643055140972, "max_range": 21.507458567619324, "mean_range": 13.552899742498994, "boundaries": [10.09860554446499, 21.507458567619324]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 130, 133, 134, 135, 138, 140, 141, 142, 143, 147, 151, 157, 158, 167, 172, 173, 178, 180, 181, 183], "size": 117, "min_range": 0.07725515402853489, "max_range": 2.892458915710449, "mean_range": 0.918669141526533, "boundaries": [-0.6470292867631575, 2.91382272895025]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 31, 32, 35, 37, 53, 54, 60, 66, 67, 79, 81, 99, 100, 112, 120, 123, 126, 127, 128, 129, 131, 137, 139, 144, 145, 146, 148, 150, 154, 155, 159, 160, 162, 163, 166, 168, 170, 171, 175, 176, 182, 184, 185], "size": 49, "min_range": 2.9519771337509155, "max_range": 6.3443381786346436, "mean_range": 4.190598557190019, "boundaries": [2.91382272895025, 6.474674744663657]}, {"group_id": 2, "name": "high", "channels": [49, 83, 86, 132, 152, 153, 169, 177], "size": 8, "min_range": 7.022891044616699, "max_range": 9.85102367401123, "mean_range": 8.131449341773987, "boundaries": [6.474674744663657, 10.035526760377065]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 156, 161, 164, 165, 174, 179], "size": 12, "min_range": 10.243053674697876, "max_range": 20.874882698059082, "mean_range": 13.674651165803274, "boundaries": [10.035526760377065, 20.874882698059082]}]}]}