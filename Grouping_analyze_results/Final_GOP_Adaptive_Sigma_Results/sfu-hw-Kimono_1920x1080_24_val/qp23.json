{"sequence": "sfu-hw-<PERSON><PERSON>_1920x1080_24_val", "qp": "qp23", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-<PERSON><PERSON>_1920x1080_24_val/qp23/codec_output/sidecar_data/minmax.npy", "n_frames": 33, "n_channels": 168, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 9, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 44, 46, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 69, 72, 75, 76, 77, 78, 81, 82, 83, 84, 87, 88, 89, 91, 92, 93, 94, 95, 97, 98, 99, 100, 101, 102, 103, 104, 106, 107, 109, 116, 117, 118, 120, 121, 123, 124, 125, 126, 130, 139, 140, 141, 142, 148, 149, 154, 155, 160, 162, 164, 167], "size": 108, "min_range": 0.0, "max_range": 3.1607744563370943, "mean_range": 1.438210219723125, "boundaries": [-0.13875140221768145, 3.2024916585335936]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 10, 11, 25, 31, 41, 43, 45, 47, 68, 70, 71, 73, 74, 85, 86, 105, 108, 110, 111, 112, 113, 114, 122, 127, 128, 129, 131, 133, 136, 137, 144, 145, 150, 152, 153, 157, 158, 163, 165, 166], "size": 42, "min_range": 3.2234051190316677, "max_range": 6.537054877728224, "mean_range": 4.347110618715218, "boundaries": [3.2024916585335936, 6.543734719284869]}, {"group_id": 2, "name": "high", "channels": [7, 79, 80, 115, 134, 135, 151, 159], "size": 8, "min_range": 6.885381832718849, "max_range": 9.04514966160059, "mean_range": 8.159048699686537, "boundaries": [6.543734719284869, 9.884977780036143]}, {"group_id": 3, "name": "ultra_high", "channels": [90, 96, 119, 132, 138, 143, 146, 147, 156, 161], "size": 10, "min_range": 10.442142147570848, "max_range": 21.191170632839203, "mean_range": 13.484085932001472, "boundaries": [9.884977780036143, 21.191170632839203]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 9, 12, 13, 14, 16, 17, 19, 20, 21, 22, 23, 24, 25, 27, 28, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 44, 45, 48, 49, 50, 51, 53, 54, 55, 56, 57, 60, 61, 62, 63, 64, 65, 66, 67, 70, 72, 73, 75, 76, 77, 78, 79, 81, 82, 83, 84, 87, 88, 89, 91, 92, 93, 94, 95, 97, 98, 99, 100, 101, 102, 103, 104, 106, 107, 109, 116, 117, 118, 120, 121, 123, 124, 125, 126, 130, 139, 140, 141, 142, 148, 149, 154, 155, 160, 162, 164, 167], "size": 105, "min_range": 0.0, "max_range": 3.1528618074953556, "mean_range": 1.1426064708192523, "boundaries": [-0.4394338238046944, 3.219120805604256]}, {"group_id": 1, "name": "medium_high", "channels": [0, 8, 10, 11, 15, 18, 26, 29, 31, 46, 47, 52, 58, 59, 68, 69, 85, 86, 105, 108, 110, 111, 112, 113, 114, 115, 122, 127, 128, 129, 131, 133, 134, 136, 137, 144, 145, 150, 152, 153, 157, 158, 163, 165, 166], "size": 45, "min_range": 3.2431662157177925, "max_range": 6.860449530184269, "mean_range": 4.599351355681817, "boundaries": [3.219120805604256, 6.877675435013206]}, {"group_id": 2, "name": "high", "channels": [43, 71, 74, 135, 146, 151, 159], "size": 7, "min_range": 7.470802158117294, "max_range": 10.325643055140972, "mean_range": 8.794093006423541, "boundaries": [6.877675435013206, 10.536230064422156]}, {"group_id": 3, "name": "ultra_high", "channels": [7, 80, 90, 96, 119, 132, 138, 143, 147, 156, 161], "size": 11, "min_range": 10.641230620443821, "max_range": 21.507458567619324, "mean_range": 13.846286714076996, "boundaries": [10.536230064422156, 21.507458567619324]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 32, "n_frames": 1, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 26, 27, 29, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 43, 46, 47, 48, 49, 51, 52, 53, 54, 55, 58, 59, 60, 61, 62, 63, 64, 65, 66, 69, 71, 72, 74, 75, 76, 77, 78, 80, 81, 82, 83, 84, 87, 88, 89, 91, 92, 93, 94, 95, 98, 99, 100, 101, 102, 103, 104, 106, 107, 109, 116, 117, 118, 120, 121, 123, 124, 125, 126, 130, 139, 140, 141, 142, 144, 148, 149, 152, 154, 155, 160, 162, 164, 167], "size": 106, "min_range": 0.0, "max_range": 3.168687105178833, "mean_range": 1.1521174487007677, "boundaries": [-0.4573103462238337, 3.1858625114629313]}, {"group_id": 1, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 25, 28, 30, 44, 45, 50, 56, 57, 67, 68, 85, 86, 97, 105, 108, 110, 111, 112, 113, 114, 122, 127, 128, 129, 131, 133, 136, 137, 145, 150, 153, 157, 158, 163, 165, 166], "size": 42, "min_range": 3.208199381828308, "max_range": 6.3443381786346436, "mean_range": 4.379834372372854, "boundaries": [3.1858625114629313, 6.829035369149697]}, {"group_id": 2, "name": "high", "channels": [41, 70, 73, 115, 134, 135, 138, 151, 159], "size": 9, "min_range": 7.022891044616699, "max_range": 10.243053674697876, "mean_range": 8.366072045432198, "boundaries": [6.829035369149697, 10.472208226836461]}, {"group_id": 3, "name": "ultra_high", "channels": [6, 79, 90, 96, 119, 132, 143, 146, 147, 156, 161], "size": 11, "min_range": 10.86856460571289, "max_range": 20.874882698059082, "mean_range": 13.986614574085582, "boundaries": [10.472208226836461, 20.874882698059082]}]}]}