{"sequence": "sfu-hw-RaceHorses_832x480_30_val", "qp": "qp24", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-RaceHorses_832x480_30_val/qp24/codec_output/sidecar_data/minmax.npy", "n_frames": 65, "n_channels": 156, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 64, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "low", "channels": [155], "size": 1, "min_range": 0.0, "max_range": 0.0, "mean_range": 0.0, "boundaries": [-4.382758761567883, 0.11667289985603535]}, {"group_id": 1, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 38, 39, 40, 41, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 64, 66, 67, 68, 69, 71, 73, 74, 75, 76, 77, 78, 79, 81, 83, 84, 87, 88, 89, 90, 91, 92, 93, 95, 96, 97, 101, 102, 104, 105, 106, 108, 109, 111, 112, 113, 114, 118, 121, 127, 128, 130, 136, 137, 140, 141, 142, 143, 148], "size": 100, "min_range": 0.6160454102932955, "max_range": 4.549778121326006, "mean_range": 2.3220067002766975, "boundaries": [0.11667289985603535, 4.616104561279954]}, {"group_id": 2, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 19, 26, 36, 37, 42, 80, 82, 85, 86, 94, 98, 99, 100, 103, 110, 115, 116, 117, 119, 122, 124, 125, 129, 132, 133, 138, 145, 150, 151, 152, 153, 154], "size": 38, "min_range": 4.627119669239178, "max_range": 8.656135342789419, "mean_range": 6.238705523474692, "boundaries": [4.616104561279954, 9.115536222703874]}, {"group_id": 3, "name": "high", "channels": [63, 65, 70, 123, 131, 139, 144, 146, 147], "size": 9, "min_range": 9.377468488884695, "max_range": 13.418004417044083, "mean_range": 11.175685782949769, "boundaries": [9.115536222703874, 13.614967884127791]}, {"group_id": 4, "name": "ultra_high", "channels": [6, 72, 107, 120, 126, 134, 135, 149], "size": 8, "min_range": 14.036141473687056, "max_range": 31.63852619041096, "mean_range": 18.78245744917711, "boundaries": [13.614967884127791, 31.63852619041096]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 18, 19, 20, 23, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 43, 44, 45, 46, 47, 49, 50, 51, 54, 55, 56, 57, 58, 59, 60, 61, 64, 66, 68, 69, 70, 71, 73, 74, 75, 76, 79, 80, 81, 83, 84, 85, 87, 88, 89, 90, 91, 92, 93, 96, 101, 102, 104, 105, 106, 108, 109, 111, 112, 113, 114, 118, 121, 127, 128, 130, 136, 137, 140, 141, 142, 143, 148, 155], "size": 92, "min_range": 0.0, "max_range": 4.310623899102211, "mean_range": 1.5734553631058905, "boundaries": [-0.43151234956838014, 4.652126365221123]}, {"group_id": 1, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 17, 21, 22, 24, 26, 41, 42, 48, 52, 53, 62, 63, 67, 77, 78, 94, 95, 97, 98, 99, 100, 103, 110, 115, 116, 117, 119, 122, 124, 125, 129, 132, 133, 138, 145, 147, 150, 151, 152, 153, 154], "size": 47, "min_range": 4.678308300673962, "max_range": 9.277626693248749, "mean_range": 6.501558198890788, "boundaries": [4.652126365221123, 9.735765080010626]}, {"group_id": 2, "name": "high", "channels": [37, 65, 86, 120, 123, 131, 139, 144, 146], "size": 9, "min_range": 10.488020211458206, "max_range": 14.724678941071033, "mean_range": 12.452027549346289, "boundaries": [9.735765080010626, 14.819403794800127]}, {"group_id": 3, "name": "ultra_high", "channels": [6, 72, 82, 107, 126, 134, 135, 149], "size": 8, "min_range": 14.914457619190216, "max_range": 31.5907199382782, "mean_range": 20.416542034596205, "boundaries": [14.819403794800127, 31.5907199382782]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 64, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 14, 16, 17, 18, 21, 22, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 41, 42, 43, 44, 45, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 59, 62, 64, 66, 67, 68, 69, 71, 72, 73, 74, 77, 78, 79, 81, 82, 83, 84, 86, 87, 88, 89, 90, 91, 92, 95, 97, 101, 102, 104, 105, 106, 108, 109, 111, 112, 113, 114, 116, 118, 121, 127, 128, 130, 136, 137, 140, 141, 142, 143, 148, 155], "size": 94, "min_range": 0.0, "max_range": 4.444737503022859, "mean_range": 1.662472766153947, "boundaries": [-0.43080036173697245, 4.580082757338786]}, {"group_id": 1, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 13, 15, 19, 20, 23, 25, 39, 40, 46, 50, 60, 61, 65, 75, 76, 93, 94, 96, 98, 99, 100, 103, 110, 115, 117, 119, 122, 124, 125, 129, 132, 133, 138, 145, 150, 151, 152, 153, 154], "size": 44, "min_range": 4.732969222646771, "max_range": 8.41383046092409, "mean_range": 6.312384970291177, "boundaries": [4.580082757338786, 9.590965876414545]}, {"group_id": 2, "name": "high", "channels": [6, 36, 63, 85, 120, 123, 131, 139, 144, 146, 147], "size": 11, "min_range": 9.630962162306815, "max_range": 14.54834321050933, "mean_range": 11.98684578822007, "boundaries": [9.590965876414545, 14.601848995490304]}, {"group_id": 3, "name": "ultra_high", "channels": [70, 80, 107, 126, 134, 135, 149], "size": 7, "min_range": 14.801484382513797, "max_range": 31.686332442543723, "mean_range": 21.23146110902101, "boundaries": [14.601848995490304, 31.686332442543723]}]}]}