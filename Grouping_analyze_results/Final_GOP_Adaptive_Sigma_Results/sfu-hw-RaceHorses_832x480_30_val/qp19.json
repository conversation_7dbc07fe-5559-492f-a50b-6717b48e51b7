{"sequence": "sfu-hw-RaceHorses_832x480_30_val", "qp": "qp19", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-RaceHorses_832x480_30_val/qp19/codec_output/sidecar_data/minmax.npy", "n_frames": 65, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 64, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 155, 156, 158, 165, 169, 170, 171, 176, 183], "size": 113, "min_range": 0.0, "max_range": 3.818257059535068, "mean_range": 1.15362489504861, "boundaries": [-0.8904107686955123, 3.997504034469571]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 130, 131, 138, 139, 143, 144, 145, 147, 149, 150, 152, 153, 157, 160, 161, 164, 166, 168, 173, 178, 179, 180, 181, 182], "size": 52, "min_range": 4.054589802342834, "max_range": 8.380798947969168, "mean_range": 6.01740118013022, "boundaries": [3.997504034469571, 8.885418837634655]}, {"group_id": 2, "name": "high", "channels": [49, 54, 83, 110, 151, 159, 167, 172, 174, 175], "size": 10, "min_range": 8.939021803212889, "max_range": 13.229419116833895, "mean_range": 11.441470899263567, "boundaries": [8.885418837634655, 13.773333640799738]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 135, 148, 154, 162, 163, 177], "size": 9, "min_range": 14.27585465540037, "max_range": 31.63852619041096, "mean_range": 19.76239542694455, "boundaries": [13.773333640799738, 31.63852619041096]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 158, 165, 169, 170, 171, 176, 183], "size": 114, "min_range": 0.0, "max_range": 3.9832943454384804, "mean_range": 1.183174083816132, "boundaries": [-0.8810206113246908, 4.027025906241306]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 157, 160, 161, 164, 166, 168, 173, 178, 179, 180, 181, 182], "size": 51, "min_range": 4.060065574944019, "max_range": 8.474207200109959, "mean_range": 6.124233377914803, "boundaries": [4.027025906241306, 8.935072423807302]}, {"group_id": 2, "name": "high", "channels": [49, 54, 83, 110, 151, 159, 167, 172, 174, 175], "size": 10, "min_range": 9.076808005571365, "max_range": 13.111821562051773, "mean_range": 11.569800370186567, "boundaries": [8.935072423807302, 13.8431189413733]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 135, 148, 154, 162, 163, 177], "size": 9, "min_range": 14.724678941071033, "max_range": 31.5907199382782, "mean_range": 19.784112801982296, "boundaries": [13.8431189413733, 31.5907199382782]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 64, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 155, 156, 158, 165, 169, 170, 171, 176, 183], "size": 113, "min_range": 0.0, "max_range": 3.783034526940548, "mean_range": 1.1488555316051798, "boundaries": [-0.9059282681380507, 3.967982162697834]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 54, 60, 66, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 130, 131, 138, 139, 143, 144, 145, 147, 149, 150, 152, 153, 157, 160, 161, 164, 166, 168, 173, 178, 179, 180, 181, 182], "size": 53, "min_range": 3.9706753528479375, "max_range": 8.600416913177028, "mean_range": 6.001715906181631, "boundaries": [3.967982162697834, 8.841892593533718]}, {"group_id": 2, "name": "high", "channels": [49, 83, 110, 151, 159, 167, 172, 174, 175], "size": 9, "min_range": 9.630962162306815, "max_range": 13.708030404466571, "mean_range": 11.614555263358737, "boundaries": [8.841892593533718, 13.715803024369603]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 135, 148, 154, 162, 163, 177], "size": 9, "min_range": 13.827030369729707, "max_range": 31.686332442543723, "mean_range": 19.7406780519068, "boundaries": [13.715803024369603, 31.686332442543723]}]}]}