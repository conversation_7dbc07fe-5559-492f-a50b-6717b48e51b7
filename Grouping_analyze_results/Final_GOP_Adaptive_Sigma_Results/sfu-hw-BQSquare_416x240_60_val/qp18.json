{"sequence": "sfu-hw-BQSquare_416x240_60_val", "qp": "qp18", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BQSquare_416x240_60_val/qp18/codec_output/sidecar_data/minmax.npy", "n_frames": 129, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 137, 140, 141, 142, 146, 149, 150, 156, 157, 159, 165, 166, 171, 172, 177], "size": 116, "min_range": 0.178631640905783, "max_range": 3.198125144896599, "mean_range": 0.9988043386478636, "boundaries": [-0.6292916686461005, 3.2038159651944587]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 31, 32, 35, 53, 60, 67, 79, 99, 100, 120, 121, 123, 126, 127, 128, 130, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 158, 161, 162, 167, 169, 170, 174, 179, 180, 181, 182, 183], "size": 45, "min_range": 3.259376692213118, "max_range": 6.566263029179893, "mean_range": 4.644967562948218, "boundaries": [3.2038159651944587, 7.036923599035018]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 81, 83, 86, 131, 152, 155, 160, 163, 173, 175, 176], "size": 14, "min_range": 7.313113254194077, "max_range": 10.416748230503156, "mean_range": 8.811909602538448, "boundaries": [7.036923599035018, 10.870031232875577]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 164, 168, 178], "size": 9, "min_range": 11.51599147076217, "max_range": 22.119557305711965, "mean_range": 15.694506616046668, "boundaries": [10.870031232875577, 22.119557305711965]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 129, 132, 133, 134, 137, 140, 141, 142, 146, 149, 150, 157, 159, 165, 166, 171, 172, 177], "size": 115, "min_range": 0.17953877797117457, "max_range": 3.2126501677557826, "mean_range": 0.9885637322660921, "boundaries": [-0.6571615776797781, 3.251657972465182]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 53, 60, 67, 79, 99, 100, 120, 123, 126, 127, 128, 130, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 156, 158, 161, 162, 167, 169, 170, 174, 179, 180, 181, 182, 183], "size": 46, "min_range": 3.2996853943914175, "max_range": 6.549773093312979, "mean_range": 4.689805755675163, "boundaries": [3.251657972465182, 7.160477522610142]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 81, 83, 86, 131, 152, 155, 160, 163, 173, 175, 176], "size": 14, "min_range": 7.193116178736091, "max_range": 10.838019974529743, "mean_range": 8.875891954371971, "boundaries": [7.160477522610142, 11.069297072755102]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 164, 168, 178], "size": 9, "min_range": 11.738426368683577, "max_range": 22.050538331270218, "mean_range": 16.069631733414198, "boundaries": [11.069297072755102, 22.050538331270218]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 137, 140, 141, 142, 146, 149, 150, 156, 157, 159, 165, 166, 171, 172, 177], "size": 116, "min_range": 0.17772450384039146, "max_range": 3.088823945705707, "mean_range": 0.987515215431334, "boundaries": [-0.6121675639026787, 3.155973957923734]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 31, 32, 35, 53, 60, 67, 79, 99, 100, 120, 121, 123, 126, 127, 128, 130, 136, 138, 139, 143, 144, 145, 147, 151, 153, 154, 158, 161, 162, 167, 169, 170, 174, 179, 180, 181, 182, 183], "size": 45, "min_range": 3.21413525893138, "max_range": 6.582752965046809, "mean_range": 4.573378405998914, "boundaries": [3.155973957923734, 6.924115479750147]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 81, 83, 86, 131, 152, 155, 160, 163, 173, 175, 176], "size": 14, "min_range": 7.0179657129141, "max_range": 10.490270702655499, "mean_range": 8.747927250704922, "boundaries": [6.924115479750147, 10.692257001576559]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 135, 148, 164, 168, 178], "size": 9, "min_range": 11.293556572840764, "max_range": 22.188576280153715, "mean_range": 15.319381498679137, "boundaries": [10.692257001576559, 22.188576280153715]}]}]}