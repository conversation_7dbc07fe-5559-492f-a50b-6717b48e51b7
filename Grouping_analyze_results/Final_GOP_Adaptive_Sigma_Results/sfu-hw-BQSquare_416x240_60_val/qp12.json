{"sequence": "sfu-hw-BQSquare_416x240_60_val", "qp": "qp12", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BQSquare_416x240_60_val/qp12/codec_output/sidecar_data/minmax.npy", "n_frames": 129, "n_channels": 186, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 130, 133, 134, 135, 138, 141, 142, 143, 147, 150, 151, 157, 158, 160, 166, 167, 172, 173, 178, 180], "size": 117, "min_range": 0.07600203826463817, "max_range": 3.1609519938293555, "mean_range": 0.964645185140896, "boundaries": [-0.6554158716142213, 3.1704432587043523]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 53, 60, 67, 79, 99, 100, 120, 121, 123, 126, 127, 128, 131, 137, 139, 140, 144, 145, 146, 148, 152, 154, 155, 159, 162, 163, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 46, "min_range": 3.198125144896599, "max_range": 6.566263029179893, "mean_range": 4.613514466903617, "boundaries": [3.1704432587043523, 6.996302389022926]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 81, 83, 86, 132, 153, 156, 161, 164, 174, 176, 177], "size": 14, "min_range": 7.313113254194077, "max_range": 10.416748230503156, "mean_range": 8.811909602538448, "boundaries": [6.996302389022926, 10.822161519341499]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 165, 169, 179], "size": 9, "min_range": 11.51599147076217, "max_range": 22.119557305711965, "mean_range": 15.694506616046668, "boundaries": [10.822161519341499, 22.119557305711965]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 121, 122, 124, 125, 129, 130, 133, 134, 135, 138, 141, 142, 143, 147, 150, 151, 158, 160, 166, 167, 172, 173, 178, 180], "size": 117, "min_range": 0.07427676321822219, "max_range": 3.2126501677557826, "mean_range": 0.9733493838177667, "boundaries": [-0.6835726282902557, 3.217753309836944]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 53, 60, 67, 79, 99, 100, 120, 123, 126, 127, 128, 131, 137, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 162, 163, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 46, "min_range": 3.2996853943914175, "max_range": 6.549773093312979, "mean_range": 4.689805755675163, "boundaries": [3.217753309836944, 7.1190792479641445]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 81, 83, 86, 132, 153, 156, 161, 164, 174, 176, 177], "size": 14, "min_range": 7.193116178736091, "max_range": 10.838019974529743, "mean_range": 8.875891954371971, "boundaries": [7.1190792479641445, 11.020405186091343]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 165, 169, 179], "size": 9, "min_range": 11.738426368683577, "max_range": 22.050538331270218, "mean_range": 16.069631733414198, "boundaries": [11.020405186091343, 22.050538331270218]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 130, 133, 134, 135, 138, 141, 142, 143, 147, 150, 151, 157, 158, 160, 166, 167, 172, 173, 178, 180], "size": 118, "min_range": 0.07772731331105416, "max_range": 3.088823945705707, "mean_range": 0.9725028249187726, "boundaries": [-0.6379097167379384, 3.12313320757176]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 31, 32, 35, 53, 60, 67, 79, 99, 100, 120, 121, 123, 126, 127, 128, 131, 137, 139, 140, 144, 145, 146, 148, 152, 154, 155, 159, 162, 163, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 45, "min_range": 3.21413525893138, "max_range": 6.582752965046809, "mean_range": 4.573378405998914, "boundaries": [3.12313320757176, 6.884176131881459]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 81, 83, 86, 132, 153, 156, 161, 164, 174, 176, 177], "size": 14, "min_range": 7.0179657129141, "max_range": 10.490270702655499, "mean_range": 8.747927250704922, "boundaries": [6.884176131881459, 10.645219056191156]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 93, 104, 110, 136, 149, 165, 169, 179], "size": 9, "min_range": 11.293556572840764, "max_range": 22.188576280153715, "mean_range": 15.319381498679137, "boundaries": [10.645219056191156, 22.188576280153715]}]}]}