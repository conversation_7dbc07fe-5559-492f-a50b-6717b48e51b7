{"sequence": "sfu-hw-BQSquare_416x240_60_val", "qp": "qp24", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BQSquare_416x240_60_val/qp24/codec_output/sidecar_data/minmax.npy", "n_frames": 129, "n_channels": 160, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "low", "channels": [159], "size": 1, "min_range": 0.0, "max_range": 0.0, "mean_range": 0.0, "boundaries": [-3.478915915261539, 0.06855632123962563]}, {"group_id": 1, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 18, 20, 21, 22, 23, 24, 25, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 42, 43, 45, 47, 48, 49, 50, 51, 52, 53, 55, 56, 58, 59, 60, 61, 62, 63, 64, 65, 67, 69, 71, 73, 74, 76, 78, 79, 80, 83, 84, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 99, 100, 105, 108, 109, 110, 112, 113, 116, 117, 118, 122, 125, 131, 132, 134, 136, 140, 141, 144, 146, 147, 152, 156], "size": 95, "min_range": 0.5242460552889567, "max_range": 3.5350746634631203, "mean_range": 1.5134301308730895, "boundaries": [0.06855632123962563, 3.6160285577407905]}, {"group_id": 2, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 17, 19, 28, 39, 41, 44, 46, 54, 57, 66, 72, 81, 82, 98, 101, 102, 103, 104, 106, 114, 115, 119, 120, 121, 123, 126, 128, 129, 133, 137, 142, 145, 149, 154, 155, 157, 158], "size": 43, "min_range": 3.635429213769161, "max_range": 6.109864809249457, "mean_range": 4.746246563465553, "boundaries": [3.6160285577407905, 7.163500794241955]}, {"group_id": 3, "name": "high", "channels": [68, 70, 75, 77, 85, 86, 107, 127, 130, 135, 138, 148, 150, 151], "size": 14, "min_range": 7.771140155597375, "max_range": 10.23359231776916, "mean_range": 8.852753520663827, "boundaries": [7.163500794241955, 10.71097303074312]}, {"group_id": 4, "name": "ultra_high", "channels": [6, 90, 111, 124, 139, 143, 153], "size": 7, "min_range": 11.51599147076217, "max_range": 22.119557305711965, "mean_range": 15.251650755324372, "boundaries": [10.71097303074312, 22.119557305711965]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 18, 19, 20, 21, 24, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 40, 41, 42, 45, 46, 47, 48, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 67, 69, 71, 72, 73, 74, 76, 77, 78, 79, 80, 82, 83, 84, 86, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 99, 100, 105, 106, 108, 109, 110, 112, 113, 115, 116, 117, 118, 122, 125, 131, 132, 134, 136, 140, 141, 144, 146, 147, 152, 156, 159], "size": 101, "min_range": 0.0, "max_range": 3.6580341067165136, "mean_range": 1.3604948496798142, "boundaries": [-0.35705149212175025, 3.6708211950504848]}, {"group_id": 1, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 17, 22, 23, 26, 43, 49, 65, 66, 81, 98, 101, 102, 103, 104, 107, 114, 119, 120, 121, 123, 126, 128, 129, 133, 137, 142, 145, 149, 150, 154, 155, 157, 158], "size": 39, "min_range": 3.962833458557725, "max_range": 7.608260795474052, "mean_range": 5.209322058309156, "boundaries": [3.6708211950504848, 7.69869388222272]}, {"group_id": 2, "name": "high", "channels": [39, 44, 54, 68, 70, 127, 130, 135, 138, 148, 151], "size": 11, "min_range": 7.751978453248739, "max_range": 10.838019974529743, "mean_range": 9.284651410511948, "boundaries": [7.69869388222272, 11.726566569394954]}, {"group_id": 3, "name": "ultra_high", "channels": [6, 75, 85, 90, 111, 124, 139, 143, 153], "size": 9, "min_range": 11.738426368683577, "max_range": 22.050538331270218, "mean_range": 16.069631733414198, "boundaries": [11.726566569394954, 22.050538331270218]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 128, "n_frames": 65, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 8, 12, 13, 15, 16, 17, 18, 20, 21, 22, 23, 26, 27, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 42, 43, 44, 47, 48, 49, 50, 52, 53, 54, 55, 56, 58, 59, 60, 61, 62, 63, 64, 65, 66, 69, 71, 73, 74, 75, 76, 78, 79, 80, 83, 84, 85, 87, 88, 89, 91, 92, 93, 94, 95, 96, 97, 99, 100, 105, 108, 109, 110, 112, 113, 116, 117, 118, 122, 125, 131, 132, 134, 136, 140, 141, 146, 147, 152, 156, 159], "size": 97, "min_range": 0.0, "max_range": 3.3467712879180906, "mean_range": 1.2538186809916971, "boundaries": [-0.29512630939142603, 3.561235920431097]}, {"group_id": 1, "name": "medium_high", "channels": [0, 7, 9, 10, 11, 14, 19, 24, 25, 28, 45, 51, 67, 68, 81, 82, 98, 101, 102, 103, 104, 106, 114, 115, 119, 120, 121, 123, 126, 128, 129, 133, 137, 142, 144, 145, 149, 154, 155, 157, 158], "size": 41, "min_range": 3.5643840881494375, "max_range": 7.064523260410016, "mean_range": 4.781067011520742, "boundaries": [3.561235920431097, 7.41759815025362]}, {"group_id": 2, "name": "high", "channels": [41, 46, 57, 70, 72, 107, 127, 130, 135, 138, 148, 150, 151], "size": 13, "min_range": 7.52689759181096, "max_range": 10.490270702655499, "mean_range": 8.838178561583778, "boundaries": [7.41759815025362, 11.273960380076144]}, {"group_id": 3, "name": "ultra_high", "channels": [6, 77, 86, 90, 111, 124, 139, 143, 153], "size": 9, "min_range": 11.293556572840764, "max_range": 22.188576280153715, "mean_range": 15.250807382204593, "boundaries": [11.273960380076144, 22.188576280153715]}]}]}