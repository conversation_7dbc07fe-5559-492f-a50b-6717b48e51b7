{"sequence": "sfu-hw-Cactus_1920x1080_50_val", "qp": "qp24", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-Cactus_1920x1080_50_val/qp24/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 158, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 11, 13, 14, 15, 16, 17, 18, 19, 20, 22, 23, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 43, 44, 45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 65, 70, 71, 72, 75, 76, 77, 78, 81, 82, 85, 86, 87, 88, 90, 91, 92, 93, 94, 95, 96, 97, 99, 102, 105, 107, 108, 109, 111, 112, 115, 116, 117, 121, 124, 127, 130, 131, 133, 139, 140, 145, 146, 151, 155], "size": 94, "min_range": 0.5894947992673031, "max_range": 4.235789987992383, "mean_range": 1.7697974703002295, "boundaries": [0.2302016154217057, 4.242500647474998]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 10, 12, 21, 24, 26, 41, 48, 64, 66, 67, 68, 69, 79, 80, 98, 100, 101, 103, 104, 106, 113, 114, 118, 119, 120, 122, 125, 128, 132, 135, 136, 141, 143, 144, 148, 153, 154, 156, 157], "size": 43, "min_range": 4.2681516079288535, "max_range": 8.092133890216548, "mean_range": 5.7413087701607095, "boundaries": [4.242500647474998, 8.25479967952829]}, {"group_id": 2, "name": "high", "channels": [38, 42, 53, 73, 74, 83, 84, 126, 134, 137, 142, 149, 150], "size": 13, "min_range": 8.523468368602071, "max_range": 12.262099255440813, "mean_range": 9.909259551929555, "boundaries": [8.25479967952829, 12.267098711581582]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 89, 110, 123, 129, 138, 147, 152], "size": 8, "min_range": 12.534490691667253, "max_range": 25.890158141878516, "mean_range": 16.032186100104205, "boundaries": [12.267098711581582, 25.890158141878516]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 11, 14, 16, 17, 18, 19, 22, 23, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 43, 44, 45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 61, 62, 65, 67, 69, 70, 71, 72, 74, 75, 76, 77, 80, 81, 82, 84, 85, 86, 87, 88, 90, 91, 92, 93, 94, 95, 96, 97, 99, 107, 108, 109, 111, 112, 115, 116, 117, 121, 124, 127, 130, 131, 133, 139, 140, 144, 145, 146, 151, 155], "size": 93, "min_range": 0.5916075948625803, "max_range": 4.158037582412362, "mean_range": 1.497036512424451, "boundaries": [-0.1287875172078783, 4.2612237021280235]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 10, 12, 13, 15, 20, 21, 24, 26, 41, 48, 63, 64, 68, 78, 79, 98, 100, 101, 102, 103, 104, 105, 106, 113, 114, 118, 119, 120, 122, 125, 128, 132, 135, 136, 141, 143, 148, 150, 153, 154, 156, 157], "size": 46, "min_range": 4.324471801519394, "max_range": 8.493084955960512, "mean_range": 5.9408692734154025, "boundaries": [4.2612237021280235, 8.651234921463924]}, {"group_id": 2, "name": "high", "channels": [38, 42, 53, 66, 89, 123, 126, 134, 137, 142, 149], "size": 11, "min_range": 8.955693077296019, "max_range": 12.853982865810394, "mean_range": 11.143205425956033, "boundaries": [8.651234921463924, 13.041246140799828]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 73, 83, 110, 129, 138, 147, 152], "size": 8, "min_range": 13.904300421476364, "max_range": 26.188228711485863, "mean_range": 17.27421287726611, "boundaries": [13.041246140799828, 26.188228711485863]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 11, 13, 15, 17, 18, 19, 20, 23, 25, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 39, 40, 43, 44, 45, 46, 47, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 66, 68, 70, 71, 72, 73, 75, 76, 77, 78, 81, 82, 83, 85, 86, 87, 88, 90, 91, 92, 93, 94, 95, 96, 98, 99, 102, 105, 107, 108, 109, 111, 112, 114, 115, 116, 117, 121, 122, 124, 130, 131, 133, 139, 140, 145, 146, 151, 155], "size": 95, "min_range": 0.5853991580731941, "max_range": 4.211831414338314, "mean_range": 1.5316419317385557, "boundaries": [-0.15026407756776994, 4.223777592821975]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 10, 12, 14, 16, 21, 22, 24, 26, 41, 48, 65, 69, 79, 80, 97, 100, 101, 103, 104, 106, 113, 118, 119, 120, 125, 126, 127, 128, 132, 135, 136, 141, 142, 143, 144, 148, 153, 154, 156, 157], "size": 44, "min_range": 4.267758727073669, "max_range": 8.595806136275783, "mean_range": 6.004203285888536, "boundaries": [4.223777592821975, 8.597819263211719]}, {"group_id": 2, "name": "high", "channels": [38, 42, 53, 64, 67, 89, 123, 134, 137, 149, 150], "size": 11, "min_range": 8.619246071035212, "max_range": 12.667244795596961, "mean_range": 10.722878223608348, "boundaries": [8.597819263211719, 12.971860933601464]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 74, 84, 110, 129, 138, 147, 152], "size": 8, "min_range": 14.088716232415402, "max_range": 25.592087572271172, "mean_range": 17.46428388899023, "boundaries": [12.971860933601464, 25.592087572271172]}]}]}