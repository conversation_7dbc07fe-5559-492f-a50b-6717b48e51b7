{"sequence": "sfu-hw-Cactus_1920x1080_50_val", "qp": "qp21", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-Cactus_1920x1080_50_val/qp21/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 182, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 18, 19, 21, 22, 24, 26, 27, 28, 29, 32, 33, 35, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 54, 55, 56, 57, 58, 60, 61, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 81, 83, 84, 86, 87, 88, 89, 90, 91, 93, 94, 95, 96, 97, 100, 101, 102, 104, 105, 106, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117, 118, 121, 123, 124, 131, 132, 133, 135, 136, 139, 140, 141, 145, 148, 155, 157, 164, 169, 170, 175], "size": 111, "min_range": 0.35118202469929005, "max_range": 3.7480912698207027, "mean_range": 1.1416818316940178, "boundaries": [-0.5206867479398833, 3.750459604467768]}, {"group_id": 1, "name": "medium_high", "channels": [0, 12, 15, 16, 17, 20, 23, 25, 30, 31, 34, 36, 52, 59, 78, 80, 98, 119, 120, 122, 125, 126, 127, 128, 129, 130, 137, 138, 142, 143, 144, 146, 149, 151, 152, 154, 156, 159, 160, 163, 165, 167, 168, 172, 177, 178, 179, 180], "size": 48, "min_range": 3.756970310019273, "max_range": 7.968436014499854, "mean_range": 5.487378844635612, "boundaries": [3.750459604467768, 8.021605956875419]}, {"group_id": 2, "name": "high", "channels": [48, 53, 65, 85, 99, 150, 158, 161, 166, 173, 174, 181], "size": 12, "min_range": 8.092133890216548, "max_range": 12.262099255440813, "mean_range": 9.638307914650536, "boundaries": [8.021605956875419, 12.29275230928307]}, {"group_id": 3, "name": "ultra_high", "channels": [11, 82, 92, 103, 109, 134, 147, 153, 162, 171, 176], "size": 11, "min_range": 12.534490691667253, "max_range": 25.890158141878516, "mean_range": 16.073007743343833, "boundaries": [12.29275230928307, 25.890158141878516]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 18, 19, 21, 22, 24, 26, 27, 28, 29, 32, 33, 35, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 54, 55, 56, 57, 58, 60, 61, 62, 63, 64, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 81, 83, 84, 86, 87, 88, 89, 90, 91, 93, 94, 95, 96, 97, 100, 101, 102, 104, 105, 106, 107, 108, 110, 112, 113, 114, 115, 116, 117, 118, 121, 123, 124, 131, 132, 133, 135, 136, 139, 140, 141, 145, 148, 155, 157, 164, 169, 170, 175], "size": 109, "min_range": 0.3629416096373461, "max_range": 3.7123203836381435, "mean_range": 1.0988835839876678, "boundaries": [-0.5146483340907628, 3.7674916730270973]}, {"group_id": 1, "name": "medium_high", "channels": [0, 12, 15, 16, 17, 20, 23, 25, 30, 31, 34, 36, 52, 59, 66, 78, 80, 85, 98, 111, 119, 120, 122, 125, 126, 127, 128, 129, 137, 138, 142, 143, 144, 146, 149, 151, 152, 154, 156, 159, 160, 163, 165, 167, 168, 172, 177, 178, 179, 180], "size": 50, "min_range": 3.8424937836825848, "max_range": 7.945738419890404, "mean_range": 5.439763157702982, "boundaries": [3.7674916730270973, 8.049631680144957]}, {"group_id": 2, "name": "high", "channels": [53, 65, 99, 130, 150, 158, 166, 173, 174, 181], "size": 10, "min_range": 8.105700261890888, "max_range": 12.18137976527214, "mean_range": 9.248072948679328, "boundaries": [8.049631680144957, 12.331771687262817]}, {"group_id": 3, "name": "ultra_high", "channels": [11, 48, 82, 92, 103, 109, 134, 147, 153, 161, 162, 171, 176], "size": 13, "min_range": 12.42566467821598, "max_range": 26.188228711485863, "mean_range": 15.495098958794888, "boundaries": [12.331771687262817, 26.188228711485863]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 13, 14, 19, 21, 22, 24, 26, 27, 28, 29, 32, 33, 35, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 49, 50, 51, 54, 55, 56, 57, 58, 60, 61, 62, 63, 64, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 79, 81, 83, 84, 86, 87, 88, 89, 90, 91, 93, 94, 95, 96, 97, 100, 101, 102, 104, 105, 106, 107, 108, 110, 111, 112, 113, 114, 115, 116, 117, 118, 121, 123, 124, 131, 132, 133, 135, 136, 139, 140, 141, 145, 148, 155, 157, 163, 164, 169, 170, 175], "size": 111, "min_range": 0.33942243976123404, "max_range": 3.6950694033593843, "mean_range": 1.1333299506671264, "boundaries": [-0.5319182764798107, 3.733427535908441]}, {"group_id": 1, "name": "medium_high", "channels": [0, 12, 15, 16, 17, 18, 20, 23, 25, 30, 31, 34, 36, 52, 59, 80, 98, 119, 120, 122, 125, 126, 127, 128, 129, 130, 137, 138, 142, 143, 144, 146, 151, 152, 154, 156, 159, 160, 165, 167, 168, 172, 177, 178, 179, 180], "size": 46, "min_range": 3.7457027254682598, "max_range": 7.8358675060850205, "mean_range": 5.332491775196689, "boundaries": [3.733427535908441, 7.998773348296693]}, {"group_id": 2, "name": "high", "channels": [48, 53, 65, 78, 85, 99, 149, 150, 158, 161, 166, 173, 174, 181], "size": 14, "min_range": 8.062690416971842, "max_range": 12.098533832665646, "mean_range": 9.35625214494152, "boundaries": [7.998773348296693, 12.264119160684945]}, {"group_id": 3, "name": "ultra_high", "channels": [11, 82, 92, 103, 109, 134, 147, 153, 162, 171, 176], "size": 11, "min_range": 12.504758227955211, "max_range": 25.592087572271172, "mean_range": 16.127457756641483, "boundaries": [12.264119160684945, 25.592087572271172]}]}]}