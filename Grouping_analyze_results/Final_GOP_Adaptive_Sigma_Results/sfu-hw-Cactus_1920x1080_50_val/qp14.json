{"sequence": "sfu-hw-Cactus_1920x1080_50_val", "qp": "qp14", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-Cactus_1920x1080_50_val/qp14/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 150, 157, 159, 166, 171, 172, 177], "size": 112, "min_range": 0.2599361697793938, "max_range": 3.707844291079902, "mean_range": 1.1032899504912344, "boundaries": [-0.550056256434869, 3.7128996218474835]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 37, 53, 60, 79, 81, 99, 112, 120, 121, 123, 126, 127, 128, 129, 130, 131, 138, 139, 143, 144, 145, 147, 151, 153, 154, 156, 158, 161, 162, 165, 167, 169, 170, 174, 179, 180, 181, 182], "size": 49, "min_range": 3.7480912698207027, "max_range": 7.968436014499854, "mean_range": 5.451883179843472, "boundaries": [3.7128996218474835, 7.9758555001298355]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 86, 100, 152, 160, 168, 175, 176, 183], "size": 11, "min_range": 8.092133890216548, "max_range": 12.04457400739193, "mean_range": 9.399781429124149, "boundaries": [7.9758555001298355, 12.238811378412189]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 83, 93, 104, 110, 135, 148, 155, 163, 164, 173, 178], "size": 12, "min_range": 12.262099255440813, "max_range": 25.890158141878516, "mean_range": 15.75543203601858, "boundaries": [12.238811378412189, 25.890158141878516]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 150, 157, 159, 166, 171, 172, 177], "size": 111, "min_range": 0.2570354511262849, "max_range": 3.7123203836381435, "mean_range": 1.0842002498346217, "boundaries": [-0.5444677159487021, 3.729627182434342]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 24, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 86, 99, 112, 120, 121, 123, 126, 127, 128, 129, 130, 138, 139, 143, 144, 145, 147, 151, 153, 154, 156, 158, 161, 162, 165, 167, 169, 170, 174, 179, 180, 181, 182], "size": 50, "min_range": 3.8424937836825848, "max_range": 7.945738419890404, "mean_range": 5.439763157702982, "boundaries": [3.729627182434342, 8.003722080817386]}, {"group_id": 2, "name": "high", "channels": [54, 66, 100, 131, 152, 160, 168, 175, 176, 183], "size": 10, "min_range": 8.105700261890888, "max_range": 12.18137976527214, "mean_range": 9.248072948679328, "boundaries": [8.003722080817386, 12.27781697920043]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 155, 163, 164, 173, 178], "size": 13, "min_range": 12.42566467821598, "max_range": 26.188228711485863, "mean_range": 15.495098958794888, "boundaries": [12.27781697920043, 26.188228711485863]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 150, 157, 159, 165, 166, 171, 172, 177], "size": 113, "min_range": 0.26283688843250275, "max_range": 3.6950694033593843, "mean_range": 1.1186855952271662, "boundaries": [-0.5607926417874274, 3.6961720612606253]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 24, 26, 31, 32, 35, 37, 53, 60, 81, 99, 120, 121, 123, 126, 127, 128, 129, 130, 131, 138, 139, 143, 144, 145, 147, 153, 154, 156, 158, 161, 162, 167, 169, 170, 174, 179, 180, 181, 182], "size": 46, "min_range": 3.7457027254682598, "max_range": 7.8358675060850205, "mean_range": 5.332491775196689, "boundaries": [3.6961720612606253, 7.953136764308677]}, {"group_id": 2, "name": "high", "channels": [49, 54, 66, 79, 86, 100, 151, 152, 160, 163, 168, 175, 176, 183], "size": 14, "min_range": 8.062690416971842, "max_range": 12.098533832665646, "mean_range": 9.35625214494152, "boundaries": [7.953136764308677, 12.210101467356731]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 83, 93, 104, 110, 135, 148, 155, 164, 173, 178], "size": 11, "min_range": 12.504758227955211, "max_range": 25.592087572271172, "mean_range": 16.127457756641483, "boundaries": [12.210101467356731, 25.592087572271172]}]}]}