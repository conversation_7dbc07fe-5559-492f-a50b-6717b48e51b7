{"sequence": "sfu-hw-RaceHorses_416x240_30_val", "qp": "qp33", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-RaceHorses_416x240_30_val/qp33/codec_output/sidecar_data/minmax.npy", "n_frames": 65, "n_channels": 152, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32}, {"gop_id": 1, "start_frame": 32, "end_frame": 64, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 11, 14, 16, 17, 18, 21, 22, 23, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 37, 38, 41, 42, 43, 44, 45, 46, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 61, 63, 64, 65, 66, 67, 70, 71, 72, 73, 76, 77, 80, 81, 82, 84, 85, 86, 87, 88, 89, 90, 93, 98, 99, 101, 102, 103, 105, 106, 108, 109, 110, 111, 113, 115, 116, 118, 124, 125, 127, 133, 134, 137, 138, 139, 140, 145], "size": 90, "min_range": 0.7155876893860598, "max_range": 4.550835942115747, "mean_range": 1.9974458364709786, "boundaries": [0.1207483814350292, 4.81156797207003]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 10, 12, 13, 15, 19, 20, 25, 35, 36, 39, 50, 59, 60, 74, 75, 78, 79, 91, 92, 94, 95, 96, 97, 100, 107, 112, 114, 119, 121, 122, 126, 129, 130, 135, 142, 144, 147, 148, 149, 150, 151], "size": 45, "min_range": 4.8398246462598, "max_range": 9.253232786939904, "mean_range": 6.546541071900163, "boundaries": [4.81156797207003, 9.502387562705032]}, {"group_id": 2, "name": "high", "channels": [40, 62, 68, 83, 120, 128, 136, 141, 143], "size": 9, "min_range": 9.574978292214148, "max_range": 12.797082034701651, "mean_range": 11.710544855774438, "boundaries": [9.502387562705032, 14.193207153340031]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 69, 104, 117, 123, 131, 132, 146], "size": 8, "min_range": 14.584166128188372, "max_range": 31.71110036969185, "mean_range": 18.949869316847376, "boundaries": [14.193207153340031, 31.71110036969185]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 31, "n_frames": 32, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 11, 12, 14, 16, 17, 18, 21, 23, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 36, 37, 38, 41, 42, 43, 44, 45, 47, 48, 49, 51, 52, 53, 54, 55, 56, 57, 58, 61, 63, 65, 66, 67, 68, 70, 71, 72, 73, 76, 77, 78, 80, 81, 82, 84, 85, 86, 87, 88, 89, 90, 93, 98, 99, 101, 102, 103, 105, 106, 108, 109, 110, 111, 113, 115, 116, 118, 124, 125, 127, 133, 134, 137, 138, 139, 140, 145], "size": 91, "min_range": 0.6143741989508271, "max_range": 4.729998376220465, "mean_range": 1.7331476241370882, "boundaries": [-0.29988740614111187, 4.849982329272973]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 10, 13, 15, 19, 20, 22, 24, 39, 40, 46, 50, 59, 60, 64, 74, 75, 91, 92, 94, 95, 96, 97, 100, 107, 112, 114, 119, 121, 122, 126, 129, 130, 135, 142, 144, 147, 148, 149, 150, 151], "size": 44, "min_range": 4.904591903090477, "max_range": 9.904677048325539, "mean_range": 6.876164402220059, "boundaries": [4.849982329272973, 9.999852064687058]}, {"group_id": 2, "name": "high", "channels": [35, 62, 79, 83, 117, 120, 128, 136, 141, 143], "size": 10, "min_range": 10.07876517623663, "max_range": 14.907230958342552, "mean_range": 12.737462355569004, "boundaries": [9.999852064687058, 15.149721800101144]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 69, 104, 123, 131, 132, 146], "size": 7, "min_range": 15.299344539642334, "max_range": 31.4633908867836, "mean_range": 21.36500328566347, "boundaries": [15.149721800101144, 31.4633908867836]}]}, {"gop_id": 1, "start_frame": 32, "end_frame": 64, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 7, 11, 13, 14, 16, 17, 18, 21, 22, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 37, 38, 41, 42, 43, 44, 46, 47, 48, 50, 51, 52, 53, 54, 55, 56, 57, 58, 61, 64, 65, 66, 67, 69, 70, 71, 72, 75, 76, 77, 79, 80, 81, 82, 84, 85, 86, 87, 88, 89, 90, 93, 98, 99, 101, 102, 103, 105, 106, 108, 109, 110, 111, 113, 115, 116, 118, 124, 125, 127, 133, 134, 137, 138, 139, 140, 145], "size": 91, "min_range": 0.6411645669828762, "max_range": 4.660343368848165, "mean_range": 1.7633616963177638, "boundaries": [-0.2793860896137197, 4.773153614867084]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 8, 9, 10, 12, 15, 19, 20, 23, 25, 39, 40, 45, 49, 59, 60, 63, 73, 74, 91, 92, 94, 95, 96, 97, 100, 107, 112, 114, 119, 121, 122, 126, 129, 130, 135, 142, 143, 144, 147, 148, 149, 150, 151], "size": 45, "min_range": 4.777865023323984, "max_range": 9.627672282132236, "mean_range": 6.730944829406563, "boundaries": [4.773153614867084, 9.825693319347888]}, {"group_id": 2, "name": "high", "channels": [36, 62, 83, 117, 120, 128, 131, 136, 141], "size": 9, "min_range": 11.281640155748887, "max_range": 14.60712456703186, "mean_range": 12.695045059176806, "boundaries": [9.825693319347888, 14.878233023828692]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 68, 78, 104, 123, 132, 146], "size": 7, "min_range": 14.887796286380652, "max_range": 31.958809852600098, "mean_range": 21.129358891284827, "boundaries": [14.878233023828692, 31.958809852600098]}]}]}