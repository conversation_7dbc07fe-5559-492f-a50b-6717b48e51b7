# 自适应西格玛分组分析总结报告

## 概述

本报告总结了对所有序列所有QP的自适应西格玛分组策略分析结果。分析基于真实的GOP结构，为每个序列的每个QP值生成了详细的通道分组策略。

## 分析范围

- **总文件数**: 56个minmax.npy文件
- **成功分析**: 56个（100%成功率）
- **序列数**: 14个不同的视频序列
- **QP范围**: 4-33（根据序列不同）

## 序列列表

1. **sfu-hw-BQMall_832x480_60_val** (4个QP: qp5, qp12, qp22, qp24)
2. **sfu-hw-BQSquare_416x240_60_val** (4个QP: qp12, qp18, qp22, qp24)
3. **sfu-hw-BQTerrace_1920x1080_60_val** (4个QP: qp7, qp9, qp10, qp13)
4. **sfu-hw-BasketballDrill_832x480_50_val** (4个QP: qp10, qp14, qp16, qp21)
5. **sfu-hw-BasketballDrive_1920x1080_50_val** (4个QP: qp4, qp9, qp12, qp16)
6. **sfu-hw-BasketballPass_416x240_50_val** (4个QP: qp10, qp19, qp22, qp24)
7. **sfu-hw-BlowingBubbles_416x240_50_val** (4个QP: qp18, qp22, qp24, qp26)
8. **sfu-hw-Cactus_1920x1080_50_val** (4个QP: qp14, qp21, qp24, qp27)
9. **sfu-hw-Kimono_1920x1080_24_val** (4个QP: qp13, qp15, qp21, qp23)
10. **sfu-hw-ParkScene_1920x1080_24_val** (4个QP: qp10, qp11, qp12, qp16)
11. **sfu-hw-PartyScene_832x480_50_val** (4个QP: qp11, qp13, qp16, qp19)
12. **sfu-hw-RaceHorses_416x240_30_val** (4个QP: qp24, qp28, qp31, qp33)
13. **sfu-hw-RaceHorses_832x480_30_val** (4个QP: qp19, qp24, qp26, qp29)
14. **sfu-hw-Traffic_2560x1600_30_val** (4个QP: qp7, qp10, qp16, qp19)

## GOP结构分析

### 真实GOP结构特征
- **所有序列都采用2个GOP结构**
- **GOP边界基于编码器日志中的I帧位置确定**
- **主要GOP模式**:
  - **64+33帧**: 97帧序列 (50fps序列)
  - **64+65帧**: 129帧序列 (60fps序列)  
  - **32+1帧**: 33帧序列 (24fps序列)
  - **32+33帧**: 65帧序列 (30fps序列)

### GOP结构修复
原始分析中发现了GOP帧数计算错误的问题：
- **问题**: 使用编码器日志的最后POC作为最后GOP的结束帧，导致帧数不匹配
- **解决方案**: 修改为使用minmax.npy的实际帧数来确定最后GOP的结束帧
- **修复效果**: 所有GOP的帧数现在与实际数据完全匹配

## 自适应西格玛分组策略

### 分组方法
基于通道动态范围的统计分布，使用μ±σ方法进行自适应分组：

1. **ultra_low**: < μ-2σ (超低动态范围)
2. **low**: μ-2σ ~ μ-σ (低动态范围)
3. **medium_low**: μ-σ ~ μ (中低动态范围)
4. **medium_high**: μ ~ μ+σ (中高动态范围)
5. **high**: μ+σ ~ μ+2σ (高动态范围)
6. **ultra_high**: > μ+2σ (超高动态范围)

### 分组特征
- **每个序列每个QP都有独特的分组策略**
- **提供整体分组（所有GOP平均）和GOP级别分组**
- **通道数量根据QP值变化**（高QP时通道数减少）
- **动态范围分布因序列内容而异**

## 输出文件结构

```
Final_GOP_Adaptive_Sigma_Results/
├── summary.txt                    # 总体摘要
├── sfu-hw-序列名/
│   ├── qpX.json                  # 详细JSON数据
│   └── qpX.txt                   # 可读性报告
└── ...
```

### JSON文件内容
- 序列和QP信息
- GOP边界定义
- 整体自适应西格玛分组
- 各GOP的分组策略
- 通道统计信息

### TXT文件内容
- GOP结构概览
- 整体分组摘要
- 各GOP分组详情
- 通道范围信息

## 应用价值

1. **编码优化**: 为不同动态范围的通道采用不同的量化策略
2. **GOP级自适应**: 支持在GOP内部进行精细化的通道管理
3. **序列特异性**: 每个序列都有定制化的分组策略
4. **QP敏感性**: 分组策略随QP值自适应调整

## 技术特点

- ✅ **真实GOP结构**: 基于编码器日志解析实际GOP边界
- ✅ **帧数精确匹配**: 修复了GOP帧数计算问题
- ✅ **统计学分组**: 使用μ±σ方法进行科学分组
- ✅ **多层次分析**: 提供整体和GOP级别的双重视角
- ✅ **完整覆盖**: 100%成功分析所有56个文件

## 结论

本分析成功为所有14个序列的56个QP配置生成了基于真实GOP结构的自适应西格玛分组策略。修复了原始分析中的GOP帧数问题，确保了分组策略的准确性和实用性。这些结果可以直接用于指导FCTM编码器的通道量化优化。
