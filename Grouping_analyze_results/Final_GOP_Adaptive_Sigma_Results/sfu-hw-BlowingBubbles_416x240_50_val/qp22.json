{"sequence": "sfu-hw-BlowingBubbles_416x240_50_val", "qp": "qp22", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BlowingBubbles_416x240_50_val/qp22/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 158, 164, 165, 171, 176, 183], "size": 115, "min_range": 0.0, "max_range": 3.6358213009820743, "mean_range": 1.1273851004611999, "boundaries": [-0.6470673659009911, 3.6927861709221164]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 86, 99, 100, 120, 121, 123, 126, 127, 128, 130, 138, 139, 143, 144, 145, 147, 150, 152, 157, 160, 161, 166, 168, 169, 170, 173, 178, 179, 180, 181], "size": 46, "min_range": 3.8570973593741655, "max_range": 7.867135547327273, "mean_range": 5.387416076459598, "boundaries": [3.6927861709221164, 8.032639707745224]}, {"group_id": 2, "name": "high", "channels": [54, 66, 131, 151, 153, 167, 172, 174, 175, 182], "size": 10, "min_range": 8.33552910878577, "max_range": 10.937017312442714, "mean_range": 9.836742670501046, "boundaries": [8.032639707745224, 12.372493244568332]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 154, 159, 162, 163, 177], "size": 13, "min_range": 12.592707280634027, "max_range": 24.437789427285843, "mean_range": 15.66421559034458, "boundaries": [12.372493244568332, 24.437789427285843]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 155, 156, 158, 164, 165, 170, 171, 176, 183], "size": 116, "min_range": 0.0, "max_range": 3.681707862764597, "mean_range": 1.147836916327241, "boundaries": [-0.6683413760968202, 3.7230299150573387]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 31, 32, 35, 37, 53, 54, 60, 67, 79, 86, 99, 100, 120, 121, 123, 126, 127, 128, 130, 138, 139, 143, 144, 145, 147, 150, 152, 157, 160, 161, 166, 168, 169, 173, 178, 179, 180, 181], "size": 45, "min_range": 3.812262002378702, "max_range": 8.065240856260061, "mean_range": 5.474669284890923, "boundaries": [3.7230299150573387, 8.114401206211497]}, {"group_id": 2, "name": "high", "channels": [66, 81, 131, 151, 153, 167, 172, 174, 175, 182], "size": 10, "min_range": 8.127788126468658, "max_range": 11.370760425925255, "mean_range": 9.998685800284147, "boundaries": [8.114401206211497, 12.505772497365657]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 154, 159, 162, 163, 177], "size": 13, "min_range": 12.706110998988152, "max_range": 24.174748048186302, "mean_range": 15.810880481050564, "boundaries": [12.505772497365657, 24.174748048186302]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 19, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 129, 132, 133, 134, 136, 137, 139, 140, 141, 142, 146, 149, 155, 156, 158, 164, 165, 171, 176, 183], "size": 116, "min_range": 0.0, "max_range": 3.6594412218440664, "mean_range": 1.1495417525472125, "boundaries": [-0.6372205717383195, 3.6625424267868945]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 21, 26, 31, 32, 35, 37, 53, 60, 67, 79, 81, 86, 99, 100, 120, 121, 123, 126, 127, 128, 130, 138, 143, 144, 145, 147, 150, 152, 157, 160, 161, 166, 168, 169, 170, 173, 178, 179, 180, 181], "size": 45, "min_range": 3.901932716369629, "max_range": 7.9165683876384385, "mean_range": 5.378272476902715, "boundaries": [3.6625424267868945, 7.962305425312108]}, {"group_id": 2, "name": "high", "channels": [54, 66, 131, 151, 153, 159, 167, 172, 174, 175, 182], "size": 11, "min_range": 8.605817361311479, "max_range": 11.373945438500607, "mean_range": 9.834953465080787, "boundaries": [7.962305425312108, 12.262068423837322]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 148, 154, 162, 163, 177], "size": 12, "min_range": 12.479303562279904, "max_range": 24.700830806385387, "mean_range": 15.862851138066764, "boundaries": [12.262068423837322, 24.700830806385387]}]}]}