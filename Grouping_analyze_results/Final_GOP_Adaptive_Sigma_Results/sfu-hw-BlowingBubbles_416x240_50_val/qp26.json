{"sequence": "sfu-hw-BlowingBubbles_416x240_50_val", "qp": "qp26", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BlowingBubbles_416x240_50_val/qp26/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 158, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "low", "channels": [157], "size": 1, "min_range": 0.0, "max_range": 0.0, "mean_range": 0.0, "boundaries": [-4.014213583797447, 0.10170698336916395]}, {"group_id": 1, "name": "medium_low", "channels": [1, 2, 3, 4, 6, 9, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 41, 43, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 60, 61, 64, 66, 68, 69, 70, 71, 73, 74, 75, 76, 78, 79, 80, 83, 84, 85, 88, 89, 90, 91, 92, 93, 94, 96, 97, 99, 103, 104, 106, 107, 108, 110, 111, 113, 114, 115, 116, 120, 123, 126, 129, 130, 132, 138, 139, 144, 145, 150, 152], "size": 97, "min_range": 0.5897198564013127, "max_range": 4.1977565479323715, "mean_range": 1.8361085869223122, "boundaries": [0.10170698336916395, 4.2176275505357745]}, {"group_id": 2, "name": "medium_high", "channels": [0, 5, 8, 10, 11, 23, 37, 40, 42, 44, 49, 53, 54, 62, 63, 67, 77, 86, 95, 98, 100, 101, 102, 112, 117, 118, 119, 121, 124, 131, 134, 135, 140, 142, 143, 147, 153, 154, 155], "size": 39, "min_range": 4.308296838944608, "max_range": 7.90539125197172, "mean_range": 5.745184354911389, "boundaries": [4.2176275505357745, 8.333548117702385]}, {"group_id": 3, "name": "high", "channels": [7, 81, 82, 87, 105, 125, 127, 141, 146, 148, 149, 156], "size": 12, "min_range": 8.645542370832779, "max_range": 10.937017312442714, "mean_range": 9.910838463263719, "boundaries": [8.333548117702385, 12.449468684868997]}, {"group_id": 4, "name": "ultra_high", "channels": [65, 72, 109, 122, 128, 133, 136, 137, 151], "size": 9, "min_range": 12.592707280634027, "max_range": 24.437789427285843, "mean_range": 16.143374294719912, "boundaries": [12.449468684868997, 24.437789427285843]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 9, 11, 13, 15, 16, 17, 19, 20, 21, 22, 25, 26, 28, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 41, 42, 45, 46, 47, 48, 50, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 64, 66, 68, 69, 70, 71, 73, 74, 75, 76, 79, 80, 81, 83, 84, 85, 86, 88, 89, 90, 91, 92, 93, 94, 96, 97, 99, 103, 104, 106, 107, 108, 110, 111, 113, 114, 115, 116, 120, 123, 126, 129, 130, 132, 138, 139, 144, 145, 150, 157], "size": 96, "min_range": 0.0, "max_range": 4.175608014687896, "mean_range": 1.497576307683559, "boundaries": [-0.2751484594962621, 4.250846228181324]}, {"group_id": 1, "name": "medium_high", "channels": [0, 8, 10, 12, 14, 18, 23, 24, 27, 29, 43, 44, 49, 54, 62, 63, 67, 77, 78, 95, 98, 100, 101, 102, 112, 117, 118, 119, 121, 124, 127, 131, 134, 135, 140, 142, 143, 147, 152, 153, 154, 155], "size": 42, "min_range": 4.276831299066544, "max_range": 8.651650067418814, "mean_range": 5.899113348707379, "boundaries": [4.250846228181324, 8.776840915858909]}, {"group_id": 2, "name": "high", "channels": [40, 65, 105, 122, 125, 141, 146, 148, 149, 156], "size": 10, "min_range": 9.571981638669968, "max_range": 13.213058471679688, "mean_range": 11.341872975975274, "boundaries": [8.776840915858909, 13.302835603536495]}, {"group_id": 3, "name": "ultra_high", "channels": [7, 72, 82, 87, 109, 128, 133, 136, 137, 151], "size": 10, "min_range": 13.313857115805149, "max_range": 24.174748048186302, "mean_range": 16.66848881095648, "boundaries": [13.302835603536495, 24.174748048186302]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 9, 10, 12, 13, 14, 16, 17, 18, 19, 22, 24, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 38, 39, 40, 43, 44, 45, 46, 48, 49, 50, 51, 52, 54, 55, 56, 57, 58, 59, 60, 61, 64, 66, 68, 69, 70, 71, 73, 74, 75, 78, 79, 80, 82, 83, 84, 85, 87, 88, 89, 90, 91, 92, 93, 94, 96, 97, 99, 103, 104, 106, 107, 108, 110, 111, 113, 114, 115, 116, 120, 123, 129, 130, 132, 138, 139, 144, 145, 150, 152, 157], "size": 96, "min_range": 0.0, "max_range": 4.132927414142724, "mean_range": 1.505685983534968, "boundaries": [-0.24300238056654244, 4.184408872890225]}, {"group_id": 1, "name": "medium_high", "channels": [0, 6, 7, 8, 11, 15, 20, 21, 23, 25, 41, 42, 47, 62, 63, 67, 76, 77, 95, 98, 100, 101, 102, 112, 117, 118, 119, 121, 124, 126, 131, 134, 135, 140, 142, 143, 147, 153, 154, 155], "size": 40, "min_range": 4.1948741963415435, "max_range": 8.605817361311479, "mean_range": 5.666446577148004, "boundaries": [4.184408872890225, 8.611820126346993]}, {"group_id": 2, "name": "high", "channels": [53, 65, 105, 122, 125, 127, 133, 136, 141, 146, 148, 149, 156], "size": 13, "min_range": 8.645836548371749, "max_range": 13.020902705915047, "mean_range": 10.609780856779405, "boundaries": [8.611820126346993, 13.039231379803761]}, {"group_id": 3, "name": "ultra_high", "channels": [5, 37, 72, 81, 86, 109, 128, 137, 151], "size": 9, "min_range": 13.595951528260201, "max_range": 24.700830806385387, "mean_range": 16.889525919249564, "boundaries": [13.039231379803761, 24.700830806385387]}]}]}