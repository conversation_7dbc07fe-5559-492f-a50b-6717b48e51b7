{"sequence": "sfu-hw-PartyScene_832x480_50_val", "qp": "qp19", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-PartyScene_832x480_50_val/qp19/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 184, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 156, 164, 165, 170, 171, 176, 183], "size": 112, "min_range": 0.0, "max_range": 3.3812490270727062, "mean_range": 0.9888689445950111, "boundaries": [-0.7985211905664316, 3.4120086499198456]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 54, 60, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 155, 157, 158, 160, 161, 166, 168, 169, 173, 178, 179, 180, 181, 182], "size": 53, "min_range": 3.4225767375054685, "max_range": 7.278865414474047, "mean_range": 4.92311864215114, "boundaries": [3.4120086499198456, 7.622538490406123]}, {"group_id": 2, "name": "high", "channels": [66, 148, 151, 162, 167, 172, 174, 175], "size": 8, "min_range": 9.147431290002935, "max_range": 11.353633778113307, "mean_range": 9.90757582087606, "boundaries": [7.622538490406123, 11.8330683308924]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 154, 159, 163, 177], "size": 11, "min_range": 12.207704140042717, "max_range": 29.009330792860553, "mean_range": 16.079125017235594, "boundaries": [11.8330683308924, 29.009330792860553]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 156, 164, 165, 171, 176, 183], "size": 111, "min_range": 0.0, "max_range": 3.3905899096280336, "mean_range": 0.9605915648668203, "boundaries": [-0.8015779125075952, 3.4038428721336085]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 54, 60, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 130, 138, 139, 143, 144, 145, 147, 150, 152, 153, 155, 157, 158, 160, 161, 166, 168, 169, 170, 173, 178, 179, 180, 181, 182], "size": 53, "min_range": 3.424699534662068, "max_range": 7.4228866919875145, "mean_range": 4.828183967303836, "boundaries": [3.4038428721336085, 7.609263656774813]}, {"group_id": 2, "name": "high", "channels": [66, 131, 148, 151, 162, 167, 172, 174, 175], "size": 9, "min_range": 7.665598310530186, "max_range": 11.145868211984634, "mean_range": 9.646615429470936, "boundaries": [7.609263656774813, 11.814684441416016]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 135, 154, 159, 163, 177], "size": 11, "min_range": 12.367792185395956, "max_range": 28.142155647277832, "mean_range": 16.088012330911376, "boundaries": [11.814684441416016, 28.142155647277832]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 132, 133, 134, 136, 137, 140, 141, 142, 146, 149, 156, 157, 164, 165, 170, 171, 176, 183], "size": 114, "min_range": 0.0, "max_range": 3.383136500011791, "mean_range": 1.036400380289563, "boundaries": [-0.807238706264024, 3.4201744277060833]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 79, 81, 86, 99, 100, 120, 121, 123, 126, 127, 128, 129, 130, 131, 138, 139, 143, 144, 145, 147, 150, 152, 153, 155, 158, 160, 161, 166, 168, 169, 173, 178, 179, 180, 181, 182], "size": 50, "min_range": 3.49396047447667, "max_range": 7.353718634807702, "mean_range": 4.944065428899997, "boundaries": [3.4201744277060833, 7.647587561676191]}, {"group_id": 2, "name": "high", "channels": [54, 66, 135, 148, 151, 162, 167, 172, 174, 175], "size": 10, "min_range": 7.819292581442631, "max_range": 11.56139934424198, "mean_range": 9.857608811783066, "boundaries": [7.647587561676191, 11.875000695646298]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 154, 159, 163, 177], "size": 10, "min_range": 11.943409789692272, "max_range": 29.87650593844327, "mean_range": 16.53830917820786, "boundaries": [11.875000695646298, 29.87650593844327]}]}]}