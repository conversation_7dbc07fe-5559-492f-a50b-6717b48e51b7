{"sequence": "sfu-hw-PartyScene_832x480_50_val", "qp": "qp13", "file_path": "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-PartyScene_832x480_50_val/qp13/codec_output/sidecar_data/minmax.npy", "n_frames": 97, "n_channels": 186, "n_gops": 2, "gop_boundaries": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33}], "overall_adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 158, 166, 167, 172, 173, 178, 180], "size": 113, "min_range": 0.0813771020612242, "max_range": 3.374960025010461, "mean_range": 0.9534790835518322, "boundaries": [-0.8237048651712726, 3.3773153508550546]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 54, 60, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 163, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 54, "min_range": 3.3812490270727062, "max_range": 7.278865414474047, "mean_range": 4.894565501131168, "boundaries": [3.3773153508550546, 7.578335566881382]}, {"group_id": 2, "name": "high", "channels": [66, 149, 153, 164, 169, 174, 176, 177], "size": 8, "min_range": 9.147431290002935, "max_range": 11.353633778113307, "mean_range": 9.90757582087606, "boundaries": [7.578335566881382, 11.77935578290771]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 156, 161, 165, 179], "size": 11, "min_range": 12.207704140042717, "max_range": 29.009330792860553, "mean_range": 16.079125017235594, "boundaries": [11.77935578290771, 29.009330792860553]}], "gop_adaptive_groups": [{"gop_id": 0, "start_frame": 0, "end_frame": 63, "n_frames": 64, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 158, 166, 167, 173, 178, 180], "size": 112, "min_range": 0.07869508856674656, "max_range": 3.3667835500091314, "mean_range": 0.9250298643766948, "boundaries": [-0.8266772749965048, 3.3692223628612084]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 54, 60, 67, 79, 81, 86, 99, 100, 112, 120, 121, 123, 126, 127, 128, 129, 131, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 159, 160, 162, 163, 168, 170, 171, 172, 175, 181, 182, 183, 184, 185], "size": 54, "min_range": 3.3905899096280336, "max_range": 7.4228866919875145, "mean_range": 4.801561855124654, "boundaries": [3.3692223628612084, 7.5651220007189215]}, {"group_id": 2, "name": "high", "channels": [66, 132, 149, 153, 164, 169, 174, 176, 177], "size": 9, "min_range": 7.665598310530186, "max_range": 11.145868211984634, "mean_range": 9.646615429470936, "boundaries": [7.5651220007189215, 11.761021638576635]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 136, 156, 161, 165, 179], "size": 11, "min_range": 12.367792185395956, "max_range": 28.142155647277832, "mean_range": 16.088012330911376, "boundaries": [11.761021638576635, 28.142155647277832]}]}, {"gop_id": 1, "start_frame": 64, "end_frame": 96, "n_frames": 33, "adaptive_sigma_groups": [{"group_id": 0, "name": "medium_low", "channels": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 14, 15, 20, 22, 23, 24, 25, 27, 28, 29, 30, 33, 34, 36, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 50, 51, 52, 55, 56, 57, 58, 59, 61, 62, 63, 64, 65, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 80, 82, 84, 85, 87, 88, 89, 90, 91, 92, 94, 95, 96, 97, 98, 101, 102, 103, 105, 106, 107, 108, 109, 111, 112, 113, 114, 115, 116, 117, 118, 119, 122, 124, 125, 130, 133, 134, 135, 137, 138, 141, 142, 143, 147, 150, 151, 158, 159, 166, 167, 172, 173, 178, 180], "size": 116, "min_range": 0.08405911555570184, "max_range": 3.383136500011791, "mean_range": 1.0217543075947104, "boundaries": [-0.8324065202160971, 3.3854083388489005]}, {"group_id": 1, "name": "medium_high", "channels": [0, 13, 16, 17, 18, 19, 21, 26, 31, 32, 35, 37, 53, 60, 79, 81, 86, 99, 100, 120, 121, 123, 126, 127, 128, 129, 131, 132, 139, 140, 144, 145, 146, 148, 152, 154, 155, 157, 160, 162, 163, 168, 170, 171, 175, 181, 182, 183, 184, 185], "size": 50, "min_range": 3.49396047447667, "max_range": 7.353718634807702, "mean_range": 4.944065428899997, "boundaries": [3.3854083388489005, 7.6032231979138984]}, {"group_id": 2, "name": "high", "channels": [54, 66, 136, 149, 153, 164, 169, 174, 176, 177], "size": 10, "min_range": 7.819292581442631, "max_range": 11.56139934424198, "mean_range": 9.857608811783066, "boundaries": [7.6032231979138984, 11.821038056978896]}, {"group_id": 3, "name": "ultra_high", "channels": [12, 49, 83, 93, 104, 110, 156, 161, 165, 179], "size": 10, "min_range": 11.943409789692272, "max_range": 29.87650593844327, "mean_range": 16.53830917820786, "boundaries": [11.821038056978896, 29.87650593844327]}]}]}