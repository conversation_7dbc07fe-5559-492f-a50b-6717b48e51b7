#!/usr/bin/env python3
"""
自适应西格玛分组分析
为所有序列所有QP生成自适应西格玛分组策略
"""

import numpy as np
import json
import os
import re
from pathlib import Path
import argparse

def find_all_minmax_files(base_dir):
    """查找所有minmax.npy文件"""
    minmax_files = []
    base_path = Path(base_dir)
    
    # 直接搜索所有minmax.npy文件
    for minmax_file in base_path.rglob("minmax.npy"):
        # 解析路径获取序列名和QP值
        parts = minmax_file.parts
        
        # 找到序列名 (sfu-hw-*)
        sequence_name = None
        qp_value = None
        
        for i, part in enumerate(parts):
            if part.startswith("sfu-hw-"):
                sequence_name = part
                # QP值应该在序列名后面
                if i + 1 < len(parts) and parts[i + 1].startswith("qp"):
                    qp_value = parts[i + 1]
                break
        
        if sequence_name and qp_value:
            minmax_files.append({
                'sequence': sequence_name,
                'qp': qp_value,
                'file_path': str(minmax_file),
                'relative_path': str(minmax_file.relative_to(base_path))
            })
    
    return minmax_files

def parse_gop_structure_from_encoder_log(minmax_file_path, actual_n_frames):
    """从编码器日志解析真实的GOP结构，使用实际的minmax帧数"""
    try:
        # 从minmax.npy路径推导编码器日志路径
        minmax_path = Path(minmax_file_path)
        codec_output_dir = minmax_path.parent.parent

        # 查找编码器日志文件
        log_files = list(codec_output_dir.glob("*_vtm_enc.log.sub_p0"))
        if not log_files:
            print(f"  警告: 未找到编码器日志文件，使用默认GOP大小")
            return None

        log_file = log_files[0]

        # 解析日志文件获取I帧位置
        i_frame_positions = []
        with open(log_file, 'r') as f:
            for line in f:
                # 匹配POC行，查找I帧 (IDR_N_LP, CRA等)
                match = re.search(r'POC\s+(\d+).*\(\s*(IDR_N_LP|CRA|IDR_W_RADL)', line)
                if match:
                    poc = int(match.group(1))
                    i_frame_positions.append(poc)

        if not i_frame_positions:
            print(f"  警告: 日志中未找到I帧信息，使用默认GOP大小")
            return None

        # 获取编码器实际编码的最后一帧POC
        last_encoded_poc = get_last_frame_from_log(log_file)



        # 构建GOP边界，使用minmax的实际帧数
        gop_boundaries = []
        for i, i_frame_pos in enumerate(i_frame_positions):
            start_frame = i_frame_pos

            if i + 1 < len(i_frame_positions):
                end_frame = i_frame_positions[i + 1] - 1
            else:
                # 最后一个GOP，使用minmax的实际帧数-1作为结束帧
                end_frame = actual_n_frames - 1

            # 确保GOP有效（至少1帧）且不超过实际帧数
            if end_frame >= start_frame and start_frame < actual_n_frames:
                # 确保end_frame不超过实际帧数
                end_frame = min(end_frame, actual_n_frames - 1)
                gop_boundaries.append({
                    'gop_id': i,
                    'start_frame': start_frame,
                    'end_frame': end_frame,
                    'n_frames': end_frame - start_frame + 1
                })

        print(f"  ✓ 从日志解析到 {len(gop_boundaries)} 个GOP")
        return gop_boundaries

    except Exception as e:
        print(f"  警告: 解析编码器日志失败: {e}，使用默认GOP大小")
        return None

def get_last_frame_from_log(log_file):
    """从日志文件获取最后一帧的POC"""
    last_poc = 0
    with open(log_file, 'r') as f:
        for line in f:
            match = re.search(r'POC\s+(\d+)', line)
            if match:
                poc = int(match.group(1))
                last_poc = max(last_poc, poc)
    return last_poc

def analyze_minmax_file(file_path, use_real_gop=True, default_gop_size=16):
    """分析单个minmax.npy文件，支持真实GOP级别分析"""
    try:
        data = np.load(file_path)

        if data.ndim == 3:
            n_frames, n_channels, _ = data.shape
        else:
            n_channels = data.shape[0]
            n_frames = 1
            data = data.reshape(1, n_channels, 2)

        # 获取GOP结构
        if use_real_gop:
            gop_boundaries = parse_gop_structure_from_encoder_log(file_path, n_frames)
            if gop_boundaries is None:
                # 回退到默认GOP大小
                n_gops = (n_frames + default_gop_size - 1) // default_gop_size
                gop_boundaries = []
                for gop_idx in range(n_gops):
                    start_frame = gop_idx * default_gop_size
                    end_frame = min(start_frame + default_gop_size, n_frames) - 1
                    gop_boundaries.append({
                        'gop_id': gop_idx,
                        'start_frame': start_frame,
                        'end_frame': end_frame,
                        'n_frames': end_frame - start_frame + 1
                    })
        else:
            # 使用默认GOP大小
            n_gops = (n_frames + default_gop_size - 1) // default_gop_size
            gop_boundaries = []
            for gop_idx in range(n_gops):
                start_frame = gop_idx * default_gop_size
                end_frame = min(start_frame + default_gop_size, n_frames) - 1
                gop_boundaries.append({
                    'gop_id': gop_idx,
                    'start_frame': start_frame,
                    'end_frame': end_frame,
                    'n_frames': end_frame - start_frame + 1
                })

        # 分析每个GOP
        gop_analyses = []
        overall_channel_stats = []

        for gop_info in gop_boundaries:
            start_frame = gop_info['start_frame']
            end_frame = gop_info['end_frame']
            gop_data = data[start_frame:end_frame+1, :, :]

            # 计算这个GOP的通道统计
            gop_channel_stats = []
            for c in range(n_channels):
                channel_data = gop_data[:, c, :]
                min_vals = channel_data[:, 0]
                max_vals = channel_data[:, 1]
                ranges = max_vals - min_vals

                stats = {
                    'channel_idx': c,
                    'mean_range': float(np.mean(ranges)),
                    'global_min': float(np.min(min_vals)),
                    'global_max': float(np.max(max_vals)),
                }
                gop_channel_stats.append(stats)

            gop_analyses.append({
                'gop_id': gop_info['gop_id'],
                'start_frame': gop_info['start_frame'],
                'end_frame': gop_info['end_frame'],
                'n_frames': gop_info['n_frames'],
                'channel_stats': gop_channel_stats
            })

        # 计算整体统计（所有GOP的平均）
        for c in range(n_channels):
            all_ranges = []
            all_mins = []
            all_maxs = []

            for gop in gop_analyses:
                all_ranges.append(gop['channel_stats'][c]['mean_range'])
                all_mins.append(gop['channel_stats'][c]['global_min'])
                all_maxs.append(gop['channel_stats'][c]['global_max'])

            overall_stats = {
                'channel_idx': c,
                'mean_range': float(np.mean(all_ranges)),
                'global_min': float(np.min(all_mins)),
                'global_max': float(np.max(all_maxs)),
                'range_std': float(np.std(all_ranges)),  # GOP间的动态范围变化
            }
            overall_channel_stats.append(overall_stats)

        return {
            'success': True,
            'n_frames': n_frames,
            'n_channels': n_channels,
            'n_gops': len(gop_boundaries),
            'gop_boundaries': gop_boundaries,
            'gop_analyses': gop_analyses,
            'overall_channel_stats': overall_channel_stats
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

def generate_adaptive_sigma_groups(channel_stats):
    """生成自适应西格玛分组"""
    ranges = [s['mean_range'] for s in channel_stats]
    mean = np.mean(ranges)
    std = np.std(ranges)
    
    # 定义边界
    boundaries = [
        -np.inf,
        mean - 2*std,  # 超低值
        mean - std,    # 低值
        mean,          # 中低值
        mean + std,    # 中高值
        mean + 2*std,  # 高值
        np.inf         # 超高值
    ]
    
    group_names = [
        'ultra_low',   # < μ-2σ
        'low',         # μ-2σ ~ μ-σ
        'medium_low',  # μ-σ ~ μ
        'medium_high', # μ ~ μ+σ
        'high',        # μ+σ ~ μ+2σ
        'ultra_high'   # > μ+2σ
    ]
    
    groups = []
    for i in range(len(boundaries) - 1):
        lower = boundaries[i]
        upper = boundaries[i + 1]
        
        # 找到在此区间内的通道
        group_channels = []
        for j, stats in enumerate(channel_stats):
            range_val = stats['mean_range']
            if i == len(boundaries) - 2:  # 最后一组包含上边界
                if lower <= range_val <= upper:
                    group_channels.append(j)
            else:
                if lower <= range_val < upper:
                    group_channels.append(j)
        
        if group_channels:
            group_ranges = [channel_stats[ch]['mean_range'] for ch in group_channels]
            groups.append({
                'group_id': len(groups),
                'name': group_names[i],
                'channels': group_channels,
                'size': len(group_channels),
                'min_range': float(np.min(group_ranges)),
                'max_range': float(np.max(group_ranges)),
                'mean_range': float(np.mean(group_ranges)),
                'boundaries': [float(lower) if lower != -np.inf else float(np.min(ranges)),
                              float(upper) if upper != np.inf else float(np.max(ranges))]
            })
    
    return groups

def main():
    parser = argparse.ArgumentParser(description='自适应西格玛分组分析')
    parser.add_argument('--base_dir',
                       default='/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup',
                       help='基础目录路径')
    parser.add_argument('--output_dir', default='Real_GOP_Adaptive_Sigma_Results', help='输出目录')
    parser.add_argument('--use_real_gop', action='store_true', default=True, help='使用真实GOP结构')
    parser.add_argument('--default_gop_size', type=int, default=16, help='默认GOP大小(当无法解析真实GOP时使用)')

    args = parser.parse_args()
    
    print(f"正在扫描目录: {args.base_dir}")
    
    # 查找所有minmax.npy文件
    minmax_files = find_all_minmax_files(args.base_dir)
    print(f"找到 {len(minmax_files)} 个minmax.npy文件")
    
    # 创建输出目录
    output_path = Path(args.output_dir)
    output_path.mkdir(exist_ok=True)
    
    # 批量分析
    print(f"\n开始自适应西格玛分组分析...")
    all_results = []
    
    for i, file_info in enumerate(minmax_files):
        sequence_name = file_info['sequence']
        qp_value = file_info['qp']
        
        print(f"[{i+1}/{len(minmax_files)}] 分析 {sequence_name} {qp_value}")

        analysis_result = analyze_minmax_file(file_info['file_path'], args.use_real_gop, args.default_gop_size)

        if analysis_result['success']:
            overall_channel_stats = analysis_result['overall_channel_stats']
            gop_analyses = analysis_result['gop_analyses']

            print(f"  ✓ {analysis_result['n_frames']}帧, {analysis_result['n_channels']}通道, {analysis_result['n_gops']}个GOP")

            # 生成整体自适应西格玛分组（基于所有GOP的平均）
            overall_adaptive_groups = generate_adaptive_sigma_groups(overall_channel_stats)

            # 为每个GOP生成分组
            gop_adaptive_groups = []
            for gop in gop_analyses:
                gop_groups = generate_adaptive_sigma_groups(gop['channel_stats'])
                gop_adaptive_groups.append({
                    'gop_id': gop['gop_id'],
                    'start_frame': gop['start_frame'],
                    'end_frame': gop['end_frame'],
                    'n_frames': gop['n_frames'],
                    'adaptive_sigma_groups': gop_groups
                })
            
            # 创建序列目录
            seq_output_dir = output_path / sequence_name
            seq_output_dir.mkdir(exist_ok=True)
            
            # 保存结果
            result = {
                'sequence': sequence_name,
                'qp': qp_value,
                'file_path': file_info['file_path'],
                'n_frames': analysis_result['n_frames'],
                'n_channels': analysis_result['n_channels'],
                'n_gops': analysis_result['n_gops'],
                'gop_boundaries': analysis_result['gop_boundaries'],
                'overall_adaptive_sigma_groups': overall_adaptive_groups,
                'gop_adaptive_groups': gop_adaptive_groups
            }

            # 保存JSON格式
            with open(seq_output_dir / f'{qp_value}.json', 'w') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)

            # 保存简洁报告
            with open(seq_output_dir / f'{qp_value}.txt', 'w') as f:
                f.write(f"{sequence_name} {qp_value} 真实GOP级自适应西格玛分组\n")
                f.write("=" * 60 + "\n\n")

                # 显示GOP结构信息
                gop_info_str = ", ".join([f"GOP{i}({gop['n_frames']}帧)" for i, gop in enumerate(analysis_result['gop_boundaries'])])
                f.write(f"数据: {analysis_result['n_frames']}帧, {analysis_result['n_channels']}通道, {analysis_result['n_gops']}个真实GOP\n")
                f.write(f"GOP结构: {gop_info_str}\n\n")

                f.write("整体自适应西格玛分组 (所有GOP平均):\n")
                for group in overall_adaptive_groups:
                    f.write(f"  {group['name']}: {group['size']}通道, "
                           f"范围 {group['min_range']:.3f}-{group['max_range']:.3f}\n")

                f.write(f"\n各GOP的分组策略:\n")
                for gop_group in gop_adaptive_groups:
                    f.write(f"\nGOP {gop_group['gop_id']} (帧 {gop_group['start_frame']}-{gop_group['end_frame']}):\n")
                    for group in gop_group['adaptive_sigma_groups']:
                        f.write(f"  {group['name']}: {group['size']}通道, "
                               f"范围 {group['min_range']:.3f}-{group['max_range']:.3f}\n")
            
            print(f"    保存到: {seq_output_dir}/{qp_value}.json")
            
        else:
            print(f"  ✗ 分析失败: {analysis_result['error']}")
        
        # 记录到总结果中
        all_results.append({
            'sequence': sequence_name,
            'qp': qp_value,
            'success': analysis_result['success'],
            'file_path': file_info['file_path']
        })
    
    # 保存总体摘要
    successful_analyses = sum(1 for r in all_results if r['success'])
    
    # 按序列分组统计
    from collections import defaultdict
    by_sequence = defaultdict(list)
    for file_info in minmax_files:
        by_sequence[file_info['sequence']].append(file_info['qp'])
    
    with open(output_path / 'summary.txt', 'w') as f:
        f.write("自适应西格玛分组分析结果\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"总文件数: {len(minmax_files)}\n")
        f.write(f"成功分析: {successful_analyses}\n")
        f.write(f"序列数: {len(by_sequence)}\n\n")
        
        f.write("每个序列的分组结果:\n")
        for seq_name in sorted(by_sequence.keys()):
            f.write(f"  {seq_name}/\n")
            for qp in sorted(by_sequence[seq_name]):
                f.write(f"    {qp}.json\n")
                f.write(f"    {qp}.txt\n")
    
    print(f"\n分析完成！结果保存在: {output_path}")
    print(f"总摘要: {output_path}/summary.txt")
    print(f"每个序列QP的分组结果保存在对应的子目录中")

if __name__ == "__main__":
    main()
