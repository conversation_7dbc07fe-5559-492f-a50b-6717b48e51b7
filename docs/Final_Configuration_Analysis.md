# FCTM 最终配置文件分析

## 概述

本文档分析了FCTM运行时生成的最终配置文件，该文件位于：
```
/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballDrill_832x480_50_val/qp10/configs/config.yaml
```

## 配置文件生成过程

### 配置合并流程

```
基础配置加载 → 层次覆盖 → 命令行覆盖 → 最终配置生成
```

1. **基础配置加载**: 加载 `eval_fctm.yaml` 和所有默认配置
2. **层次覆盖**: 应用 `compressai_vision_fctm.yaml` 覆盖
3. **命令行覆盖**: 应用所有 `++参数` 设置
4. **最终配置生成**: 保存为 `config.yaml`

## 最终配置文件详细分析

### 1. 路径信息 (第1-5行)

```yaml
paths:
  _common_root: ./logs
  _run_root: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
  configs: ${codec.output_dir}/configs
  src: ${codec.output_dir}/src
```

**说明**:
- **运行根目录**: 所有输出文件的根目录
- **配置保存路径**: 当前配置文件的保存位置

### 2. 环境信息 (第6-26行)

```yaml
env:
  system:
    hostname: amax
    username: tianshuchang
    utc_start_time: 1749455053939
misc:
  device:
    nn_parts: cpu
  seed: 1234
```

**说明**:
- 记录运行环境信息
- 设备配置为CPU
- 随机种子设置

### 3. 数据集配置 (第27-51行)

```yaml
dataset:
  type: Detectron2Dataset
  datacatalog: SFUHW
  config:
    root: /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballDrill_832x480_50_val
    dataset_name: sfu-hw-BasketballDrill_832x480_50_val
    annotation_file: annotations/BasketballDrill_832x480_50_val.json
```

**关键信息**:
- **数据集类型**: Detectron2Dataset (用于目标检测)
- **数据目录**: SFU-HW对象检测数据集
- **序列名称**: BasketballDrill_832x480_50_val
- **标注文件**: JSON格式的COCO标注

### 4. 视觉模型配置 (第57-107行)

```yaml
vision_model:
  arch: faster_rcnn_X_101_32x8d_FPN_3x
  faster_rcnn_X_101_32x8d_FPN_3x:
    cfg: models/detectron2/configs/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x.yaml
    weights: weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl
    splits: fpn
```

**说明**:
- 使用Faster R-CNN X-101 FPN架构
- 预训练权重来自COCO数据集
- 分割点设置为FPN层

### 5. 流水线配置 (第108-144行)

```yaml
pipeline:
  name: split-inference
  type: video
  output_dir_root: ${paths._run_root}/${.name}-${.type}
  codec:
    sidecar_path: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballDrill_832x480_50_val/qp10/codec_output/sidecar_data
```

**关键配置**:
- **流水线类型**: 视频分割推理
- **每通道量化**: `sidecar_path` 参数启用每通道量化
- **输出目录**: 结构化的输出路径

### 6. FCTM编解码器配置 (第145-204行)

#### 6.1 基本配置

```yaml
codec:
  type: fctm
  eval_encode: bitrate
  experiment: fcm7.0_everysignaling_SFU
  output_dir: ${pipeline.output_dir_root}/${.type}${.experiment}/${dataset.datacatalog}/${dataset.config.dataset_name}/qp${codec.tools.inner_codec.enc_configs.qp}
```

#### 6.2 编码配置

```yaml
enc_configs:
  qp: 10
  frame_rate: 50
  intra_period: 64
  n_bit: 10
  parallel_encoding: true
  hash_check: 0
```

**参数说明**:
- **QP值**: 10 (来自run_all.py的命令行覆盖)
- **帧率**: 50fps (BasketballDrill序列特定)
- **帧内周期**: 64帧 (序列特定配置)
- **量化位深**: 10位
- **并行编码**: 启用

#### 6.3 FCTM工具链配置

```yaml
tools:
  feature_reduction:
    type: pre-trained
    learned_model:
      name: light_fedrnet
      split_ctx: obj
    channel_removal:
      enabled: true

  conversion:
    type: tensor_packing
    resized_channel_packing:
      enabled: true

  inner_codec:
    type: vtm
    stash_outputs: false
```

**工具链说明**:
- **特征降维**: 使用预训练的light_fedrnet模型
- **通道移除**: 启用自适应通道移除
- **张量转换**: 使用张量打包方式
- **内部编解码器**: VTM-23.3

#### 6.4 VTM配置

```yaml
inner_codec:
  codec_paths:
    _root: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3
    enc_exe: ${._root}/bin/EncoderAppStatic
    dec_exe: ${._root}/bin/DecoderAppStatic
    cfg_file: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg
```

## 关键配置参数分析

### 每通道量化配置

**关键参数**: 
```yaml
pipeline.codec.sidecar_path: /path/to/sidecar_data
```

**作用**:
- 启用每通道量化模式
- 指定side-car数据存储位置
- 每个通道使用独立的min/max值进行量化

### 序列特定参数

| 参数 | 值 | 来源 | 说明 |
|------|----|----- |------|
| `frame_rate` | 50 | 序列配置字典 | BasketballDrill序列的帧率 |
| `intra_period` | 64 | 序列配置字典 | 帧内周期设置 |
| `qp` | 10 | run_all.py | 量化参数 |
| `parallel_encoding` | true | 命令行覆盖 | 并行编码启用 |

### 实验标识

```yaml
experiment: fcm7.0_everysignaling_SFU
```

这个标识符表明：
- **版本**: FCM 7.0
- **模式**: 每通道量化 (everysignaling)
- **数据集**: SFU-HW

## 配置文件的作用

### 1. 记录完整执行参数
- 保存了所有运行时使用的参数
- 包括所有覆盖和合并后的最终值

### 2. 支持重现实验
```bash
# 可以使用这个配置文件完全重现相同的实验
compressai-split-inference --config-path=/path/to/configs --config-name=config.yaml
```

### 3. 调试和分析
- 查看实际使用的所有配置值
- 验证参数覆盖是否正确
- 确认路径和设置是否符合预期

### 4. 配置验证
- 确认所有覆盖和合并是否正确
- 检查每通道量化是否正确启用
- 验证VTM路径和参数设置

## 重要发现

从这个配置文件可以确认：

### ✅ 每通道量化已正确启用
```yaml
sidecar_path: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmfcm7.0_everysignaling_SFU/SFUHW/sfu-hw-BasketballDrill_832x480_50_val/qp10/codec_output/sidecar_data
```

### ✅ 实验配置正确
- 实验名称: `fcm7.0_everysignaling_SFU`
- 序列: BasketballDrill_832x480_50_val
- QP: 10

### ✅ 路径解析完整
- 所有路径都已解析为绝对路径
- 没有未解析的变量引用

### ✅ VTM集成正确
- VTM路径: `/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3`
- 配置文件: `encoder_lowdelay_vtm.cfg`

## 使用建议

### 1. 配置验证
在运行实验前，检查生成的config.yaml文件：
```bash
cat /path/to/output/configs/config.yaml | grep -E "(sidecar_path|qp|experiment)"
```

### 2. 重现实验
保存config.yaml文件用于实验重现：
```bash
cp /path/to/output/configs/config.yaml ./experiment_configs/
```

### 3. 调试问题
当实验出现问题时，首先检查config.yaml中的关键参数是否正确。

---

**文档版本**: v1.0  
**最后更新**: 2025年1月  
**适用版本**: FCTM v7.0  
**示例配置**: BasketballDrill_832x480_50_val, QP10
