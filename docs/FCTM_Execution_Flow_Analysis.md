# FCTM 完整执行流程分析

## 概述

本文档详细分析从 `scripts/evaluation/test_signaling/run_encode_kimono.sh` 开始的完整FCTM执行流程，包括每个脚本的作用、配置文件、终端输出等。

## 执行流程图

```
run_encode_kimono.sh
        ↓
fctm_eval_on_sfu_hw_obj.sh
        ↓
compressai-split-inference (Python命令)
        ↓
CompressAI-Vision Pipeline
        ↓
FCTM Feature Coding Model
        ↓
VTM Inner Codec
```

## 详细执行流程分析

### 阶段1: 启动脚本 - `run_encode_kimono.sh`

#### **脚本位置**: `scripts/evaluation/test_signaling/run_encode_kimono.sh`

#### **主要功能**:
- 设置实验参数
- 配置每通道量化的sidecar路径
- 调用主评估脚本

#### **关键参数设置**:
```bash
TESTDATA_PATH="/work/Users/<USER>/fcm_testdata"
INNER_CODEC_PATH="/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3"
OUTPUT_DIR="/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output"
EXP_NAME="_original"
DEVICE="cpu"
QP=16
SEQ_NAME="Traffic_2560x1600_30_val"
```

#### **每通道量化配置**:
```bash
SIDECAR_DIR="${OUTPUT_DIR}/split-inference-video/fctm${EXP_NAME}/SFUHW/sfu-hw-${SEQ_NAME}/qp${QP}/codec_output/sidecar_data"
mkdir -p ${SIDECAR_DIR}
```

#### **终端输出**:
```
开始对 Traffic_2560x1600_30_val 进行编码 (QP=16)...
```

#### **调用下一阶段**:
```bash
bash scripts/evaluation/sfu_hw_obj/fctm_eval_on_sfu_hw_obj.sh \
    --testdata "$TESTDATA_PATH" \
    --inner_codec "$INNER_CODEC_PATH" \
    --output_dir "$OUTPUT_DIR" \
    --exp_name "$EXP_NAME" \
    --device "$DEVICE" \
    --qp "$QP" \
    --seq_name "$SEQ_NAME" \
    --extra_params "++pipeline.codec.sidecar_path=${SIDECAR_DIR}"
```

### 阶段2: 主评估脚本 - `fctm_eval_on_sfu_hw_obj.sh`

#### **脚本位置**: `scripts/evaluation/sfu_hw_obj/fctm_eval_on_sfu_hw_obj.sh`

#### **主要功能**:
- 解析命令行参数
- 设置序列特定参数（帧率、帧内周期）
- 构建CompressAI-Vision命令
- 执行完整的编解码评估流水线

#### **参数解析** (第16-47行):
- 处理所有传入的参数
- 设置环境变量

#### **序列配置映射** (第57-103行):
```bash
declare -A intra_period_dict
declare -A fr_dict

intra_period_dict["Traffic_2560x1600_30_val"]=32
fr_dict["Traffic_2560x1600_30_val"]=30
```

#### **环境变量设置**:
```bash
export DNNL_MAX_CPU_ISA=AVX2
export DEVICE=${DEVICE}
```

#### **终端输出**:
```
============================== RUNNING FCTM + COMPRESSAI-VISION ==================================
Datatset location:   /work/Users/<USER>/fcm_testdata
Output directory:    /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
Experiment folder:   fctm_original
Running Device:      cpu
Input sequence:      Traffic_2560x1600_30_val
Seq. Framerate:      30
QP for Inner Codec:  16
Intra Period for Inner Codec: 32
Other Parameters:    ++pipeline.codec.sidecar_path=/path/to/sidecar_data
==================================================================================================
```

#### **CompressAI-Vision命令构建** (第119-145行):
```bash
compressai-split-inference --config-name=eval_fctm.yaml \
    --config-dir=../../../cfgs \
    codec=compressai_vision_fctm.yaml \
    ++pipeline.type=video \
    ++paths._run_root=${OUTPUT_DIR} \
    ++vision_model.arch=faster_rcnn_X_101_32x8d_FPN_3x \
    ++dataset.type=Detectron2Dataset \
    ++dataset.datacatalog=SFUHW \
    ++dataset.config.root=${DATASET_SRC}/${SEQ} \
    ++dataset.config.annotation_file=annotations/${SEQ}.json \
    ++dataset.config.dataset_name=sfu-hw-${SEQ} \
    ++evaluator.type=COCO-EVAL \
    ++evaluator.overwrite_results=True \
    ++codec.experiment=${EXPERIMENT} \
    ++codec.enc_configs.frame_rate=${FRAME_RATE} \
    ++codec.enc_configs.intra_period=${INTRA_PERIOD} \
    ++codec.enc_configs.parallel_encoding=True \
    ++codec.enc_configs.qp=${QP} \
    ++codec.tools.inner_codec.stash_outputs=False \
    ++codec.tools.inner_codec.type=vtm \
    ++codec.tools.inner_codec.codec_paths._root=${INNER_CODEC_PATH} \
    ++codec.tools.inner_codec.codec_paths.cfg_file=${INNER_CODEC_PATH}/cfg/encoder_lowdelay_vtm.cfg \
    ++codec.tools.feature_reduction.learned_model.split_ctx=obj \
    ++codec.eval_encode=bitrate \
    ++codec.verbosity=0 \
    ++codec.device.all=${DEVICE} \
    ++misc.device.nn_parts=${DEVICE} \
    ++pipeline.codec.sidecar_path=${SIDECAR_DIR}
```

### 阶段3: CompressAI-Vision Pipeline

#### **入口点**: `compressai-split-inference` 命令

#### **实际执行文件**: `compressai_vision/compressai_vision/run/eval_split_inference.py`

#### **主要配置文件**:
1. **主配置**: `cfgs/eval_fctm.yaml`
2. **编解码器配置**: `cfgs/codec/compressai_vision_fctm.yaml`

#### **终端输出示例**:
```
[2025-01-XX XX:XX:XX,XXX][HYDRA] Launching job locally
[2025-01-XX XX:XX:XX,XXX][HYDRA] 
#######################################################
#                    HYDRA CONFIG                    #
#######################################################
Config path: /work/Users/<USER>/fctm-v7.0/cfgs
Config name: eval_fctm.yaml
Overrides: ['codec=compressai_vision_fctm.yaml', '++pipeline.type=video', ...]

[2025-01-XX XX:XX:XX,XXX][INFO] - Starting split inference pipeline
[2025-01-XX XX:XX:XX,XXX][INFO] - Loading vision model: faster_rcnn_X_101_32x8d_FPN_3x
[2025-01-XX XX:XX:XX,XXX][INFO] - Loading dataset: SFUHW/Traffic_2560x1600_30_val
[2025-01-XX XX:XX:XX,XXX][INFO] - Initializing FCTM codec
```

#### **流水线执行阶段**:

**1. 初始化阶段**:
```
[INFO] - Initializing vision model
[INFO] - Loading pre-trained weights
[INFO] - Setting up dataset loader
[INFO] - Configuring FCTM codec
[INFO] - Initializing VTM inner codec
```

**2. 特征提取阶段 (NN-Part1)**:
```
[INFO] - Processing frame 1/N
[INFO] - Extracting features from backbone
[INFO] - Feature tensor shape: [C, H, W]
[INFO] - Applying feature reduction
```

**3. 特征编码阶段**:
```
[INFO] - Starting feature encoding
[INFO] - Channel removal: X channels -> Y channels
[INFO] - Tensor to frame conversion
[INFO] - Applying per-channel quantization
[INFO] - Saving sidecar data to: /path/to/sidecar_data/minmax.npy
[INFO] - VTM encoding: input.yuv -> output.bin
```

**4. 特征解码阶段**:
```
[INFO] - Starting feature decoding
[INFO] - VTM decoding: output.bin -> reconstructed.yuv
[INFO] - Frame to tensor conversion
[INFO] - Loading sidecar data from: /path/to/sidecar_data/minmax.npy
[INFO] - Applying feature restoration
```

**5. 任务评估阶段 (NN-Part2)**:
```
[INFO] - Running object detection on restored features
[INFO] - Post-processing detection results
[INFO] - Computing COCO evaluation metrics
```

**6. 结果输出阶段**:
```
[INFO] - Saving evaluation results
[INFO] - Bitrate: XXXX kbps
[INFO] - mAP: XX.XX%
[INFO] - Encoding time: XX.XX seconds
[INFO] - Decoding time: XX.XX seconds
```

### 阶段4: FCTM Feature Coding Model

#### **核心类**: `fctm.libs.fc_model.feature_coding_model`

#### **编码流程**:

**1. 特征降维** (`FT_RDCT`):
```python
# 使用预训练的light_fedrnet模型
reduced_features = self.feature_reduction(input_features)
```

**2. 张量转换** (`CNVRS`):
```python
# 3D张量 -> 2D帧
frames = self.tensor_to_frame(reduced_features, sidecar_path)
```

**3. 内部编码** (`INNER_CDC`):
```python
# VTM编码
bitstream = self.inner_codec.encode(frames, qp=16)
```

#### **解码流程**:

**1. 内部解码**:
```python
# VTM解码
reconstructed_frames = self.inner_codec.decode(bitstream)
```

**2. 张量重建**:
```python
# 2D帧 -> 3D张量
reconstructed_features = self.frame_to_tensor(reconstructed_frames, sidecar_path)
```

**3. 特征恢复** (`FT_RSTR`):
```python
# 特征恢复
restored_features = self.feature_restoration(reconstructed_features)
```

### 阶段5: VTM Inner Codec

#### **编码命令**:
```bash
EncoderAppStatic -i input.yuv -c encoder_lowdelay_vtm.cfg -q 16 \
    -wdt 192 -hgt 276 --IntraPeriod=32 --InputChromaFormat=400 \
    --InputBitDepth=10 --FrameRate=30
```

#### **解码命令**:
```bash
DecoderAppStatic -b output.bin -o reconstructed.yuv
```

#### **VTM终端输出**:
```
VVCSoftware: VTM Encoder Version 23.3
Input          File                    : input.yuv
Bitstream      File                    : output.bin
Reconstruction File                    : rec.yuv
Real     Format                       : 192x276 30Hz
Internal Format                       : 192x276 30Hz
Sequence PSNR Y                       : 45.2345 dB
Sequence PSNR U                       : inf dB
Sequence PSNR V                       : inf dB
Total Frames                          : 10
Total Time                            : 12.345 sec.
Total Bits                            : 123456 bits (15.432 kbps)
```

## 最终输出文件结构

```
scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/
├── codec_output/
│   ├── sidecar_data/
│   │   └── minmax.npy                    # 每通道量化参数
│   ├── Traffic_2560x1600_30_val_qp16_*.yuv  # VTM输入文件
│   ├── Traffic_2560x1600_30_val_qp16_*.bin  # VTM输出比特流
│   └── *.log                             # VTM编解码日志
├── configs/
│   └── config.yaml                       # 最终合并配置
├── evaluation/
│   ├── summary.csv                       # 性能摘要
│   ├── encode_details_16.csv            # 编码详情
│   └── *.json                           # 检测结果
└── src/
    ├── compressai_vision.patch          # 代码补丁
    ├── pip_list.txt                     # Python包列表
    └── requirements.txt                 # 依赖列表
```

## 关键配置文件

### 1. `cfgs/eval_fctm.yaml`
- 定义流水线基础结构
- 设置默认参数

### 2. `cfgs/codec/compressai_vision_fctm.yaml`
- FCTM编解码器配置
- 工具链参数设置

### 3. `encoder_lowdelay_vtm.cfg`
- VTM编码器配置
- 低延迟编码参数

## 执行时间估算

- **初始化**: 10-30秒
- **特征提取**: 1-5分钟/帧
- **特征编码**: 30秒-2分钟/帧
- **特征解码**: 10-30秒/帧
- **任务评估**: 30秒-1分钟/帧
- **总时间**: 10-60分钟（取决于序列长度和复杂度）

## 详细终端输出示例

### 完整执行日志示例

```bash
$ bash scripts/evaluation/test_signaling/run_encode_kimono.sh

开始对 Traffic_2560x1600_30_val 进行编码 (QP=16)...
============================== RUNNING FCTM + COMPRESSAI-VISION ==================================
Datatset location:   /work/Users/<USER>/fcm_testdata
Output directory:    /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
Experiment folder:   fctm_original
Running Device:      cpu
Input sequence:      Traffic_2560x1600_30_val
Seq. Framerate:      30
QP for Inner Codec:  16
Intra Period for Inner Codec: 32
Other Parameters:    ++pipeline.codec.sidecar_path=/path/to/sidecar_data
==================================================================================================

[2025-01-XX 10:30:15,123][HYDRA] Launching job locally
[2025-01-XX 10:30:15,124][HYDRA]
#######################################################
#                    HYDRA CONFIG                    #
#######################################################
Config path: /work/Users/<USER>/fctm-v7.0/cfgs
Config name: eval_fctm.yaml
Overrides: ['codec=compressai_vision_fctm.yaml', '++pipeline.type=video', ...]

[2025-01-XX 10:30:16,456][INFO][compressai_vision.run.eval_split_inference] - Starting split inference pipeline
[2025-01-XX 10:30:16,789][INFO][compressai_vision.pipelines.split_inference] - Initializing pipeline components
[2025-01-XX 10:30:17,012][INFO][compressai_vision.models.vision] - Loading vision model: faster_rcnn_X_101_32x8d_FPN_3x
[2025-01-XX 10:30:25,345][INFO][compressai_vision.models.vision] - Vision model loaded successfully
[2025-01-XX 10:30:25,678][INFO][compressai_vision.datasets] - Loading dataset: SFUHW/Traffic_2560x1600_30_val
[2025-01-XX 10:30:26,901][INFO][compressai_vision.datasets] - Found 10 frames in dataset
[2025-01-XX 10:30:27,234][INFO][fctm.libs.fc_model] - Initializing FCTM codec
[2025-01-XX 10:30:27,567][INFO][fctm.libs.fc_model] - Loading feature reduction model: light_fedrnet
[2025-01-XX 10:30:30,890][INFO][fctm.libs.fc_model] - FCTM codec initialized successfully
[2025-01-XX 10:30:31,123][INFO][fctm.inner_codec.std_codecs] - Initializing VTM inner codec
[2025-01-XX 10:30:31,456][INFO][fctm.inner_codec.std_codecs] - VTM path: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3

[2025-01-XX 10:30:32,789][INFO][compressai_vision.pipelines.split_inference] - Starting processing...

=== Frame 1/10 ===
[2025-01-XX 10:30:33,012][INFO][compressai_vision.pipelines.split_inference] - Processing frame 1
[2025-01-XX 10:30:33,345][INFO][compressai_vision.models.vision] - Extracting features from backbone
[2025-01-XX 10:30:35,678][INFO][compressai_vision.models.vision] - Feature extraction completed, shape: [186, 192, 276]
[2025-01-XX 10:30:35,901][INFO][fctm.libs.libenc.encode] - Starting feature encoding
[2025-01-XX 10:30:36,234][INFO][fctm.libs.libenc.ft_reduction] - Applying feature reduction
[2025-01-XX 10:30:37,567][INFO][fctm.libs.libenc.ft_reduction] - Channel removal: 186 -> 186 channels (QP=16)
[2025-01-XX 10:30:37,890][INFO][fctm.libs.libenc.ft_conversion] - Converting tensor to frame
[2025-01-XX 10:30:38,123][INFO][fctm.libs.libenc.ft_conversion] - Applying per-channel quantization
[2025-01-XX 10:30:38,456][INFO][fctm.libs.libenc.ft_conversion] - Saving sidecar data: minmax.npy
[2025-01-XX 10:30:38,789][INFO][fctm.inner_codec.std_codecs] - VTM encoding started
[2025-01-XX 10:30:39,012][INFO][fctm.inner_codec.std_codecs] - Input: Traffic_frame001_192x276_30fps_10bit_p400_input.yuv
[2025-01-XX 10:30:39,345][INFO][fctm.inner_codec.std_codecs] - Command: EncoderAppStatic -i input.yuv -c encoder_lowdelay_vtm.cfg -q 16 -wdt 192 -hgt 276 --IntraPeriod=32 --InputChromaFormat=400 --InputBitDepth=10

VVCSoftware: VTM Encoder Version 23.3 [Linux][GCC 9.4.0][64 bit]

Input          File                    : Traffic_frame001_192x276_30fps_10bit_p400_input.yuv
Bitstream      File                    : Traffic_frame001_192x276_30fps_10bit_p400_output.bin
Reconstruction File                    : Traffic_frame001_192x276_30fps_10bit_p400_rec.yuv
Real     Format                       : 192x276 30Hz
Internal Format                       : 192x276 30Hz
Frame index                           : 0 - 0 (1 frames)
Total Frames                          : 1
Frame Rate                            : 30
Frame Skip                            : 0
Temporal layer max                    : 0
Random Access Period                  : 32
QP                                    : 16
Max dQP signaling depth               : 0

started @ Tue Jan XX 10:30:39 2025
POC    0 TId: 0 ( I-SLICE, QP 16 )     123456 bits [Y 45.2345 dB    U inf dB    V inf dB] [ET    12 ] [L0 ] [L1 ]
ended @ Tue Jan XX 10:30:51 2025
Total Time:       12.345 sec.

SUMMARY --------------------------------------------------------
Total Frames |   Bitrate     Y-PSNR    U-PSNR    V-PSNR    YUV-PSNR
        1    a    15432.0     45.235      inf       inf      45.235

[2025-01-XX 10:30:51,678][INFO][fctm.inner_codec.std_codecs] - VTM encoding completed
[2025-01-XX 10:30:51,901][INFO][fctm.inner_codec.std_codecs] - Output bitstream: 123456 bits
[2025-01-XX 10:30:52,234][INFO][fctm.libs.libdec.decode] - Starting feature decoding
[2025-01-XX 10:30:52,567][INFO][fctm.inner_codec.std_codecs] - VTM decoding started
[2025-01-XX 10:30:52,890][INFO][fctm.inner_codec.std_codecs] - Command: DecoderAppStatic -b output.bin -o reconstructed.yuv

VVCSoftware: VTM Decoder Version 23.3 [Linux][GCC 9.4.0][64 bit]

Bitstream      File                    : Traffic_frame001_192x276_30fps_10bit_p400_output.bin
Reconstruction File                    : Traffic_frame001_192x276_30fps_10bit_p400_dec.yuv
Real     Format                       : 192x276 30Hz
Frames                                : 1
started @ Tue Jan XX 10:30:53 2025
POC    0 TId: 0 ( I-SLICE, QP 16 )
ended @ Tue Jan XX 10:30:55 2025
Total Time:        2.123 sec.

[2025-01-XX 10:30:55,123][INFO][fctm.inner_codec.std_codecs] - VTM decoding completed
[2025-01-XX 10:30:55,456][INFO][fctm.libs.libdec.ft_invconversion] - Converting frame to tensor
[2025-01-XX 10:30:55,789][INFO][fctm.libs.libdec.ft_invconversion] - Loading sidecar data: minmax.npy
[2025-01-XX 10:30:56,012][INFO][fctm.libs.libdec.ft_restoration] - Applying feature restoration
[2025-01-XX 10:30:57,345][INFO][fctm.libs.libdec.decode] - Feature decoding completed
[2025-01-XX 10:30:57,678][INFO][compressai_vision.models.vision] - Running object detection on restored features
[2025-01-XX 10:30:59,901][INFO][compressai_vision.models.vision] - Detection completed, found 15 objects
[2025-01-XX 10:31:00,234][INFO][compressai_vision.pipelines.split_inference] - Frame 1 processing completed

=== Frame 2/10 ===
[继续处理其他帧...]

=== 评估阶段 ===
[2025-01-XX 10:45:30,567][INFO][compressai_vision.evaluation] - Starting COCO evaluation
[2025-01-XX 10:45:31,890][INFO][compressai_vision.evaluation] - Computing detection metrics
[2025-01-XX 10:45:32,123][INFO][compressai_vision.evaluation] - mAP: 0.7524
[2025-01-XX 10:45:32,456][INFO][compressai_vision.evaluation] - mAP@0.5: 0.8234
[2025-01-XX 10:45:32,789][INFO][compressai_vision.evaluation] - mAP@0.75: 0.7123

=== 结果保存 ===
[2025-01-XX 10:45:33,012][INFO][compressai_vision.pipelines.split_inference] - Saving results
[2025-01-XX 10:45:33,345][INFO][compressai_vision.pipelines.split_inference] - Results saved to: /path/to/output/evaluation/
[2025-01-XX 10:45:33,678][INFO][compressai_vision.pipelines.split_inference] - Summary:
  - Total frames: 10
  - Average bitrate: 3176.0 kbps
  - Average mAP: 75.24%
  - Total encoding time: 123.45 seconds
  - Total decoding time: 21.23 seconds

任务完成
编码完成！
如需执行解码评估，请运行对应的解码评估脚本。
```

## 常见错误和解决方案

### 1. VTM路径错误
```bash
[ERROR][fctm.inner_codec.std_codecs] - VTM encoder not found: /path/to/VTM/bin/EncoderAppStatic
[ERROR][fctm.inner_codec.std_codecs] - Please check INNER_CODEC_PATH setting
```
**解决**: 检查VTM安装路径和可执行文件权限

### 2. 数据集路径错误
```bash
[ERROR][compressai_vision.datasets] - Dataset not found: /path/to/dataset
[ERROR][compressai_vision.datasets] - Please check TESTDATA_PATH setting
```
**解决**: 确认数据集路径和文件结构

### 3. 内存不足
```bash
[ERROR][fctm.libs.fc_model] - CUDA out of memory
[ERROR][fctm.libs.fc_model] - Try reducing batch size or using CPU
```
**解决**: 切换到CPU模式或增加GPU内存

### 4. 配置文件错误
```bash
[ERROR][HYDRA] - Config file not found: eval_fctm.yaml
[ERROR][HYDRA] - Please check config path
```
**解决**: 检查配置文件路径和内容

### 5. 权限错误
```bash
[ERROR][fctm.inner_codec.std_codecs] - Permission denied: /path/to/output
[ERROR][fctm.inner_codec.std_codecs] - Please check directory permissions
```
**解决**: 检查输出目录权限

## 性能监控指标

### 编码性能
- **特征提取时间**: 通常2-5秒/帧
- **VTM编码时间**: 通常10-30秒/帧
- **内存使用**: 4-8GB (CPU模式)

### 解码性能
- **VTM解码时间**: 通常1-3秒/帧
- **特征恢复时间**: 通常1-2秒/帧

### 质量指标
- **mAP**: 目标检测精度
- **比特率**: 压缩后的数据率
- **PSNR**: 特征重建质量

---

**文档版本**: v1.0
**最后更新**: 2025年1月
**适用版本**: FCTM v7.0
