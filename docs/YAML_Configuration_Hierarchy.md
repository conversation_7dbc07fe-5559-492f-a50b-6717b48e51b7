# FCTM YAML配置文件层次结构分析

## 概述

本文档详细分析了运行 `/work/Users/<USER>/fctm-v7.0/scripts/evaluation/run_all.py` 时使用的YAML配置文件层次结构和加载顺序。

## 执行流程和YAML文件使用顺序

当运行 `run_all.py` 时，会按以下顺序使用YAML配置文件：

### 1. 主入口配置文件

**文件**: `compressai_vision/cfgs/eval_fctm.yaml`

**作用**: 主配置文件，定义整个流水线的基础结构

**关键内容**: 
- 引用其他默认配置文件
- 设置流水线类型为 `split_inference`
- 设置编解码器为 `fctm`

```yaml
defaults:
  - paths: default
  - env: default
  - misc: default
  - dataset: default
  - evaluator: default
  - vision_model: default
  - pipeline: split_inference
  - codec: fctm
  - _self_

pipeline:
  type: "video"
  codec:
    encode_only: False

vision_model:
  arch: faster_rcnn_X_101_32x8d_FPN_3x

dataset:
  type: "Detectron2Dataset"
  datacatalog: 'SFUHW'
```

### 2. 流水线配置文件

**文件**: `compressai_vision/cfgs/pipeline/split_inference.yaml`

**作用**: 定义分割推理流水线的结构和参数

**关键内容**: 
- 设置输出目录结构
- 配置NN任务的两个部分
- 定义编解码和评估流程

```yaml
name: "split-inference"
type: "video"
output_dir_root: ${paths._run_root}/${.name}-${.type}
datatype: float32

nn_task_part1:
    load_features: False
    dump_features: False
    generate_features_only: False

codec:
    encode_only: False
    decode_only: False

nn_task_part2:
    dump_results: False

evaluation:
    bypass: False
    dump: True
```

### 3. 编解码器配置文件 (被覆盖)

**文件**: `cfgs/codec/compressai_vision_fctm.yaml`

**作用**: FCTM编解码器的具体配置

**重要**: 这个文件会**覆盖**默认的 `fctm.yaml`

**关键内容**: 
- FCTM工具链配置
- VTM内部编解码器设置
- 特征降维和恢复参数

```yaml
type: "fctm"
eval_encode: "bitrate"
experiment: ""
output_dir: "${pipeline.output_dir_root}/${.type}${.experiment}/${dataset.datacatalog}/${dataset.config.dataset_name}/qp${codec.tools.inner_codec.enc_configs.qp}"

tools:
  feature_reduction:
    type: 'pre-trained'
    learned_model:
      name: 'light_fedrnet'
      split_ctx: "obj"
    channel_removal:
      enabled: True

  conversion:
    type: 'tensor_packing'
    resized_channel_packing:
      enabled: True

  inner_codec:
    type: 'vtm'
    stash_outputs: False
    inner_coding_max_chroma_format: 0
    inner_coding_max_pred_constraint: 1
```

### 4. 默认配置文件组

这些文件通过 `defaults` 部分自动加载：

#### 4.1 路径配置
**文件**: `compressai_vision/cfgs/paths/default.yaml`
- **作用**: 定义各种路径变量

#### 4.2 环境配置
**文件**: `compressai_vision/cfgs/env/default.yaml`
- **作用**: 环境变量和系统设置

#### 4.3 数据集配置
**文件**: `compressai_vision/cfgs/dataset/default.yaml`
- **作用**: 数据集加载和处理参数

#### 4.4 评估器配置
**文件**: `compressai_vision/cfgs/evaluator/default.yaml`
- **作用**: 评估指标和方法设置

#### 4.5 视觉模型配置
**文件**: `compressai_vision/cfgs/vision_model/default.yaml`
- **作用**: 神经网络模型架构设置

### 5. VTM编解码器配置文件

**文件**: `{VTM_PATH}/cfg/encoder_lowdelay_vtm.cfg`
- **作用**: VTM编解码器的底层配置
- **位置**: 在VTM软件包内部

## 配置文件优先级和覆盖关系

### 优先级顺序 (从高到低)

1. **命令行参数** (`++参数名=值`)
2. **compressai_vision_fctm.yaml** (通过 `codec=` 指定)
3. **eval_fctm.yaml** (主配置文件)
4. **默认配置文件组** (通过 `defaults` 加载)

### 关键覆盖示例

在 `fctm_eval_on_sfu_hw_obj.sh` 中，通过命令行参数覆盖配置：

```bash
compressai-split-inference --config-name=eval_fctm.yaml \
    --config-dir=../../../cfgs \
    codec=compressai_vision_fctm.yaml \
    ++pipeline.type=video \
    ++codec.enc_configs.qp=${QP} \
    ++codec.tools.inner_codec.codec_paths._root=${INNER_CODEC_PATH} \
    ++dataset.datacatalog=SFUHW \
    ++vision_model.arch=faster_rcnn_X_101_32x8d_FPN_3x \
    ++evaluator.type=COCO-EVAL
```

## 配置文件的作用域

### 全局配置
- `eval_fctm.yaml`: 整个流水线的全局设置
- `split_inference.yaml`: 分割推理流程控制

### 编解码器配置
- `compressai_vision_fctm.yaml`: FCTM特定配置
- `encoder_lowdelay_vtm.cfg`: VTM底层编解码参数

### 任务特定配置
- `dataset/default.yaml`: 数据集处理
- `vision_model/default.yaml`: 神经网络模型
- `evaluator/default.yaml`: 评估指标

## 实际运行时的配置合并

当 `run_all.py` 运行时，Hydra框架会按以下顺序合并配置：

1. **加载基础配置**: `eval_fctm.yaml` + 所有默认配置
2. **应用编解码器覆盖**: `compressai_vision_fctm.yaml`
3. **应用命令行覆盖**: 所有 `++参数` 设置
4. **生成最终配置**: 用于实际执行

## 关键配置参数的来源

| 参数 | 来源文件 | 说明 |
|------|----------|------|
| `pipeline.type` | `eval_fctm.yaml` | 设置为 "video" |
| `codec.type` | `compressai_vision_fctm.yaml` | 设置为 "fctm" |
| `codec.enc_configs.qp` | 命令行覆盖 | 来自 `run_all.py` |
| `dataset.datacatalog` | 命令行覆盖 | 设置为 "SFUHW" |
| `vision_model.arch` | 命令行覆盖 | 设置为 Faster R-CNN |
| `codec.tools.inner_codec.type` | `compressai_vision_fctm.yaml` | 设置为 "vtm" |
| `codec.tools.feature_reduction.learned_model.name` | `compressai_vision_fctm.yaml` | 设置为 "light_fedrnet" |

## 每通道量化的关键配置

在 `run_all.py` 中，每通道量化通过以下参数启用：

```python
sidecar_dir = f"{output_dir}/split-inference-video/fctm{output_name}/SFUHW/sfu-hw-{seq_name}/qp{seq_qp}/codec_output/sidecar_data"
task_command = f"... ++pipeline.codec.sidecar_path={sidecar_dir}"
```

这个参数会在最终配置中体现为：
```yaml
pipeline:
  codec:
    sidecar_path: /path/to/sidecar_data
```

## 配置文件层次化的优势

这种层次化的配置系统提供了以下优势：

1. **模块化**: 不同功能的配置分离
2. **可重用性**: 默认配置可以在多个实验中重用
3. **灵活性**: 可以通过命令行轻松覆盖特定参数
4. **可维护性**: 配置变更影响范围明确
5. **可追溯性**: 最终配置文件记录了所有实际使用的参数

## 调试配置的建议

1. **查看最终配置**: 检查生成的 `config.yaml` 文件
2. **验证覆盖**: 确认命令行参数是否正确应用
3. **检查路径**: 确保所有路径都正确解析
4. **对比默认值**: 与原始配置文件对比，确认修改生效

---

**文档版本**: v1.0  
**最后更新**: 2025年1月  
**适用版本**: FCTM v7.0
