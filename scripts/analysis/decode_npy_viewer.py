#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解码端NPY文件查看器

专门用于查看FCTM解码端导出的npy文件的详细数值
"""

import numpy as np
import os
import sys
from datetime import datetime

def view_decoded_packed_frame(file_path):
    """查看解码后的打包帧"""
    data = np.load(file_path)
    print("🔍 解码后打包帧详细信息:")
    print(f"  形状: {data.shape} (帧高度, 帧宽度)")
    print(f"  数据类型: {data.dtype}")
    print(f"  数值范围: [{data.min():.0f}, {data.max():.0f}]")
    print(f"  统计: 均值={data.mean():.2f}, 标准差={data.std():.2f}")
    
    print(f"\n  前5个和后5个通道的所有数值:")
    
    # 前5行
    print(f"  前5行:")
    for h in range(min(5, data.shape[0])):
        row_str = f"    行{h:2d}: " + " ".join([f"{val:6.0f}" for val in data[h]])
        print(row_str)
    
    # 后5行
    if data.shape[0] > 5:
        print(f"\n  后5行:")
        for h in range(data.shape[0]-5, data.shape[0]):
            row_str = f"    行{h:2d}: " + " ".join([f"{val:6.0f}" for val in data[h]])
            print(row_str)

def view_dequantized_tensor(file_path):
    """查看逆量化张量"""
    data = np.load(file_path)
    print("🔍 逆量化张量详细信息:")
    print(f"  形状: {data.shape}")
    print(f"  数值范围: [{data.min():.6f}, {data.max():.6f}] (应该是[0,1])")
    print(f"  统计: 均值={data.mean():.6f}, 标准差={data.std():.6f}")

    # 检查是否正确逆量化
    if data.min() >= 0 and data.max() <= 1:
        print("  ✅ 逆量化正确: 数值在[0,1]范围内")
    else:
        print("  ⚠️  逆量化异常: 数值超出[0,1]范围")

    # 处理不同的维度情况
    if len(data.shape) == 4 and data.shape[0] == 1:
        # 形状是 (1, C, H, W)，去掉第一个维度
        data = data[0]
        print(f"  实际数据形状: {data.shape} (通道数, 高度, 宽度)")
    elif len(data.shape) == 3:
        # 形状是 (C, H, W)
        print(f"  数据形状: {data.shape} (通道数, 高度, 宽度)")
    else:
        print(f"  ⚠️  未知的数据形状: {data.shape}")
        return

    print("\n  前5个和后5个通道的所有数值:")
    for c in range(min(5, data.shape[0])):
        print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
        channel_data = data[c]
        for h in range(channel_data.shape[0]):
            row_str = "      " + " ".join([f"{float(val):8.4f}" for val in channel_data[h]])
            print(row_str)
        print()

    if data.shape[0] > 5:
        print(f"  后5个通道的所有数值:")
        for c in range(data.shape[0]-5, data.shape[0]):
            print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
            channel_data = data[c]
            for h in range(channel_data.shape[0]):
                row_str = "      " + " ".join([f"{float(val):8.4f}" for val in channel_data[h]])
                print(row_str)
            print()

def view_unpacked_tensor(file_path):
    """查看逆打包张量"""
    data = np.load(file_path)
    print("🔍 逆打包张量详细信息:")
    print(f"  形状: {data.shape}")
    print(f"  数值范围: [{data.min():.6f}, {data.max():.6f}]")
    print(f"  统计: 均值={data.mean():.6f}, 标准差={data.std():.6f}")

    # 处理不同的维度情况
    if len(data.shape) == 4 and data.shape[0] == 1:
        # 形状是 (1, C, H, W)，去掉第一个维度
        data = data[0]
        print(f"  实际数据形状: {data.shape} (通道数, 高度, 宽度)")
    elif len(data.shape) == 3:
        # 形状是 (C, H, W)
        print(f"  数据形状: {data.shape} (通道数, 高度, 宽度)")
    else:
        print(f"  ⚠️  未知的数据形状: {data.shape}")
        return

    print("\n  前5个和后5个通道的所有数值:")
    for c in range(min(5, data.shape[0])):
        print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
        channel_data = data[c]
        for h in range(channel_data.shape[0]):
            row_str = "      " + " ".join([f"{float(val):8.4f}" for val in channel_data[h]])
            print(row_str)
        print()

    if data.shape[0] > 5:
        print(f"  后5个通道的所有数值:")
        for c in range(data.shape[0]-5, data.shape[0]):
            print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
            channel_data = data[c]
            for h in range(channel_data.shape[0]):
                row_str = "      " + " ".join([f"{float(val):8.4f}" for val in channel_data[h]])
                print(row_str)
            print()

def view_reconstructed_tensor(file_path):
    """查看重建特征张量"""
    data = np.load(file_path)
    print("🔍 重建特征张量详细信息:")
    print(f"  形状: {data.shape}")
    print(f"  数据类型: {data.dtype}")
    print(f"  数值范围: [{data.min():.6f}, {data.max():.6f}]")
    print(f"  统计: 均值={data.mean():.6f}, 标准差={data.std():.6f}")

    # 处理不同的维度情况
    if len(data.shape) == 4 and data.shape[0] == 1:
        # 形状是 (1, C, H, W)，去掉第一个维度
        data = data[0]
        print(f"  实际数据形状: {data.shape} (通道数, 高度, 宽度)")
    elif len(data.shape) == 3:
        # 形状是 (C, H, W)
        print(f"  数据形状: {data.shape} (通道数, 高度, 宽度)")
    else:
        print(f"  ⚠️  未知的数据形状: {data.shape}")
        return

    print("\n  前5个和后5个通道的所有数值:")
    for c in range(min(5, data.shape[0])):
        print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
        channel_data = data[c]
        for h in range(channel_data.shape[0]):
            row_str = "      " + " ".join([f"{float(val):8.4f}" for val in channel_data[h]])
            print(row_str)
        print()

    if data.shape[0] > 5:
        print(f"  后5个通道的所有数值:")
        for c in range(data.shape[0]-5, data.shape[0]):
            print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
            channel_data = data[c]
            for h in range(channel_data.shape[0]):
                row_str = "      " + " ".join([f"{float(val):8.4f}" for val in channel_data[h]])
                print(row_str)
            print()

    # 显示极值通道
    channel_mins = np.min(data, axis=(1,2))
    channel_maxs = np.max(data, axis=(1,2))
    min_channel = np.argmin(channel_mins)
    max_channel = np.argmax(channel_maxs)

    print(f"  极值通道:")
    print(f"    最小值通道 {min_channel}: {channel_mins[min_channel]:.6f}")
    print(f"    最大值通道 {max_channel}: {channel_maxs[max_channel]:.6f}")

def main():
    """
    主函数
    """
    if len(sys.argv) != 2:
        print("用法: python decode_npy_viewer.py <decode_frame_folder_path>")
        print("例如: python decode_npy_viewer.py /path/to/exported_decode_data/frame_0000")
        return
    
    frame_folder = sys.argv[1]
    
    if not os.path.exists(frame_folder):
        print(f"❌ 文件夹不存在: {frame_folder}")
        return
    
    # 创建输出txt文件
    output_txt = os.path.join(frame_folder, "detailed_decode_analysis.txt")
    
    print("=" * 80)
    print(f"解码端NPY文件查看器 - {os.path.basename(frame_folder)}")
    print("=" * 80)
    print(f"输出将保存到: {output_txt}")
    
    # 4个解码端文件的查看函数映射
    file_viewers = {
        "decoded_packed_frame.npy": view_decoded_packed_frame,
        "dequantized_tensor.npy": view_dequantized_tensor,
        "unpacked_tensor.npy": view_unpacked_tensor,
        "reconstructed_tensor.npy": view_reconstructed_tensor,
    }
    
    # 重定向输出到文件
    import io
    from contextlib import redirect_stdout
    
    output_buffer = io.StringIO()
    
    with redirect_stdout(output_buffer):
        print("=" * 80)
        print(f"解码端NPY文件详细分析 - {os.path.basename(frame_folder)}")
        print("=" * 80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"源文件夹: {frame_folder}")
        print()
        
        for filename, viewer_func in file_viewers.items():
            file_path = os.path.join(frame_folder, filename)
            
            print(f"\n{'='*60}")
            print(f"📁 {filename}")
            print(f"{'='*60}")
            
            if os.path.exists(file_path):
                try:
                    viewer_func(file_path)
                except Exception as e:
                    print(f"❌ 处理文件时出错: {e}")
            else:
                print(f"❌ 文件不存在: {file_path}")
        
        print(f"\n{'='*80}")
        print("✅ 解码端详细查看完成!")
        print(f"{'='*80}")
    
    # 保存到txt文件
    with open(output_txt, 'w', encoding='utf-8') as f:
        f.write(output_buffer.getvalue())
    
    # 同时在控制台显示
    print(output_buffer.getvalue())
    
    # 显示文件信息
    file_size = os.path.getsize(output_txt) / 1024  # KB
    print(f"\n✅ 解码端分析结果已保存到: {output_txt}")
    print(f"📊 文件大小: {file_size:.1f} KB")

if __name__ == "__main__":
    main()
