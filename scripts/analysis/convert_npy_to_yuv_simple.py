#!/usr/bin/env python3
"""
简单的NPY到YUV转换工具
将特征张量按网格排列，生成YUV 4:0:0格式文件和对应的图片
"""

import numpy as np
import os
import math
import matplotlib.pyplot as plt

def convert_to_simple_yuv(npy_path, output_dir=None, bit_depth=10, normalize=True):
    """
    将npy文件转换为YUV 4:0:0文件

    参数:
    npy_path: npy文件路径
    output_dir: 输出目录
    bit_depth: 位深度
    normalize: 是否进行归一化 (True=归一化, False=保持原始值)
    """
    norm_type = "归一化" if normalize else "原始值"
    print(f"🔄 转换为YUV 4:0:0: {npy_path} ({norm_type})")

    # 检查文件是否存在
    if not os.path.exists(npy_path):
        print(f"❌ 文件不存在: {npy_path}")
        return None

    # 加载npy文件
    features = np.load(npy_path)
    print(f"✅ 成功加载npy文件")
    print(f"📊 原始特征形状: {features.shape}")

    if features.ndim != 3:
        print(f"❌ 期望3维数据 (C, H, W)，但得到 {features.ndim} 维")
        return None

    C, H, W = features.shape

    # 计算网格布局 - 简单的正方形排列
    cols = int(math.ceil(math.sqrt(C)))
    rows = int(math.ceil(C / cols))

    print(f"📐 简单网格布局:")
    print(f"   通道数: {C}")
    print(f"   网格: {rows}行 × {cols}列")
    print(f"   总位置: {rows * cols}")
    print(f"   未使用位置: {rows * cols - C}")

    # 计算帧尺寸
    frame_width = cols * W
    frame_height = rows * H

    print(f"   帧尺寸: {frame_width} × {frame_height}")

    # 创建输出帧
    packed_frame = np.zeros((frame_height, frame_width), dtype=np.float32)

    print(f"📦 打包特征:")

    # 打包每个通道
    for i in range(C):
        # 计算在网格中的位置
        row_idx = i // cols
        col_idx = i % cols

        # 计算在帧中的位置
        start_h = row_idx * H
        start_w = col_idx * W
        end_h = start_h + H
        end_w = start_w + W

        # 放置通道数据
        packed_frame[start_h:end_h, start_w:end_w] = features[i]

        if i < 5:  # 只显示前5个通道的信息
            print(f"   通道 {i}: 位置({start_h}:{end_h}, {start_w}:{end_w})")

    # 处理数据
    print(f"\n📊 数据处理:")
    print(f"   原始数据范围: [{features.min():.6f}, {features.max():.6f}]")

    if normalize:
        # Min-Max归一化到[0, 1]
        min_val = features.min()
        max_val = features.max()
        normalized_frame = (packed_frame - min_val) / (max_val - min_val)

        # 量化到指定位深度
        max_quant_value = (2 ** bit_depth) - 1
        quantized_frame = np.round(normalized_frame * max_quant_value)

        print(f"   归一化后范围: [0.0, 1.0]")
        print(f"   量化后范围: [{quantized_frame.min():.0f}, {quantized_frame.max():.0f}]")

        # 转换为合适的数据类型
        if bit_depth <= 8:
            quantized_frame = quantized_frame.astype(np.uint8)
        else:
            quantized_frame = quantized_frame.astype(np.uint16)

        # 用于图片显示的数据
        display_frame = normalized_frame
        min_val_for_params = min_val
        max_val_for_params = max_val
    else:
        # 保持原始值，不进行归一化
        quantized_frame = packed_frame.astype(np.float32)
        display_frame = packed_frame
        min_val_for_params = features.min()
        max_val_for_params = features.max()

        print(f"   保持原始值范围: [{quantized_frame.min():.6f}, {quantized_frame.max():.6f}]")

    # 确定输出路径
    if output_dir is None:
        output_dir = os.path.dirname(npy_path)

    os.makedirs(output_dir, exist_ok=True)

    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(npy_path))[0]
    norm_suffix = "normalized" if normalize else "original"
    yuv_filename = f"{base_name}_simple_{norm_suffix}_{frame_width}x{frame_height}.yuv"
    yuv_path = os.path.join(output_dir, yuv_filename)

    # 保存YUV文件 (4:0:0格式，只有Y分量)
    print(f"\n💾 保存YUV 4:0:0文件: {yuv_path}")
    with open(yuv_path, 'wb') as f:
        quantized_frame.tofile(f)

    file_size = os.path.getsize(yuv_path)
    print(f"📊 YUV文件大小: {file_size} 字节")

    # 同时生成对应的图片文件
    img_filename = f"{base_name}_simple_global_{norm_suffix}.png"
    img_path = os.path.join(output_dir, img_filename)

    print(f"🖼️ 生成对应图片: {img_path}")
    plt.figure(figsize=(12, 8))

    if normalize:
        plt.imshow(display_frame, cmap='gray', aspect='auto', vmin=0, vmax=1)
        plt.colorbar(label='Normalized Value (Global)')
        title_suffix = "Normalized"
    else:
        plt.imshow(display_frame, cmap='gray', aspect='auto')
        plt.colorbar(label='Original Value (Global)')
        title_suffix = "Original Values"

    plt.title(f'{base_name} - Global {title_suffix} (Grayscale)\nShape: {frame_width}×{frame_height} ({C} channels)')
    plt.xlabel('Width')
    plt.ylabel('Height')
    plt.savefig(img_path, dpi=150, bbox_inches='tight')
    plt.close()

    img_size = os.path.getsize(img_path)
    print(f"📊 图片文件大小: {img_size} 字节")

    # 保存参数文件
    params_filename = f"{base_name}_simple_params.txt"
    params_path = os.path.join(output_dir, params_filename)

    with open(params_path, 'w') as f:
        f.write(f"# YUV 4:0:0格式参数\n")
        f.write(f"normalized={normalize}\n")
        f.write(f"min_value={min_val_for_params:.8f}\n")
        f.write(f"max_value={max_val_for_params:.8f}\n")
        if normalize:
            f.write(f"bit_depth={bit_depth}\n")
        f.write(f"frame_width={frame_width}\n")
        f.write(f"frame_height={frame_height}\n")
        f.write(f"original_shape={features.shape}\n")
        f.write(f"channels={C}\n")
        f.write(f"grid_layout={rows}x{cols}\n")
        f.write(f"channel_size={H}x{W}\n")
        f.write(f"yuv_file={yuv_path}\n")
        f.write(f"image_file={img_path}\n")

    print(f"✅ 转换完成!")
    print(f"📁 YUV文件: {yuv_path}")
    print(f"📁 图片文件: {img_path}")
    print(f"📁 参数文件: {params_path}")

    return yuv_path, img_path, params_path


def convert_to_channel_quantized_yuv(npy_path, minmax_path, frame_idx=0, output_dir=None, bit_depth=10):
    """
    使用每通道min/max值进行量化的YUV转换

    参数:
    npy_path: npy文件路径
    minmax_path: minmax.npy文件路径
    frame_idx: 使用的帧索引（默认第0帧）
    output_dir: 输出目录
    bit_depth: 位深度
    """
    print(f"🔄 转换为每通道量化YUV: {npy_path}")
    print(f"📊 使用minmax文件: {minmax_path}")
    print(f"🎯 使用第{frame_idx}帧的min/max值")

    # 检查文件是否存在
    if not os.path.exists(npy_path):
        print(f"❌ 特征文件不存在: {npy_path}")
        return None

    if not os.path.exists(minmax_path):
        print(f"❌ MinMax文件不存在: {minmax_path}")
        return None

    # 加载npy文件
    features = np.load(npy_path)
    print(f"✅ 成功加载特征文件")
    print(f"📊 原始特征形状: {features.shape}")

    if features.ndim != 3:
        print(f"❌ 期望3维数据 (C, H, W)，但得到 {features.ndim} 维")
        return None

    # 加载minmax文件
    minmax_data = np.load(minmax_path)
    print(f"✅ 成功加载MinMax文件")
    print(f"📊 MinMax数据形状: {minmax_data.shape}")

    if minmax_data.ndim != 3 or minmax_data.shape[2] != 2:
        print(f"❌ 期望MinMax数据形状为 (N_frames, N_channels, 2)，但得到 {minmax_data.shape}")
        return None

    # 检查帧索引是否有效
    if frame_idx >= minmax_data.shape[0]:
        print(f"❌ 帧索引 {frame_idx} 超出范围，最大帧索引为 {minmax_data.shape[0]-1}")
        return None

    # 获取指定帧的min/max值
    frame_minmax = minmax_data[frame_idx]  # shape: (N_channels, 2)
    print(f"📊 第{frame_idx}帧的MinMax形状: {frame_minmax.shape}")

    C, H, W = features.shape

    # 检查通道数是否匹配
    if C != frame_minmax.shape[0]:
        print(f"⚠️  通道数不匹配: 特征有{C}个通道，MinMax有{frame_minmax.shape[0]}个通道")
        # 使用较小的通道数
        C = min(C, frame_minmax.shape[0])
        print(f"📊 使用前{C}个通道进行转换")

    # 计算网格布局
    cols = int(math.ceil(math.sqrt(C)))
    rows = int(math.ceil(C / cols))

    print(f"📐 每通道量化网格布局:")
    print(f"   通道数: {C}")
    print(f"   网格: {rows}行 × {cols}列")
    print(f"   总位置: {rows * cols}")
    print(f"   未使用位置: {rows * cols - C}")

    # 计算帧尺寸
    frame_width = cols * W
    frame_height = rows * H

    print(f"   帧尺寸: {frame_width} × {frame_height}")

    # 创建输出帧
    packed_frame = np.zeros((frame_height, frame_width), dtype=np.float32)

    print(f"📦 打包特征并进行每通道量化:")

    # 量化到指定位深度
    max_quant_value = (2 ** bit_depth) - 1

    # 打包每个通道并进行每通道量化
    for i in range(C):
        # 计算在网格中的位置
        row_idx = i // cols
        col_idx = i % cols

        # 计算在帧中的位置
        start_h = row_idx * H
        start_w = col_idx * W
        end_h = start_h + H
        end_w = start_w + W

        # 获取当前通道的min/max值
        channel_min = frame_minmax[i, 0]
        channel_max = frame_minmax[i, 1]

        # 获取当前通道的特征数据
        channel_data = features[i]

        # 每通道归一化到[0, 1]
        if channel_max > channel_min:
            normalized_channel = (channel_data - channel_min) / (channel_max - channel_min)
        else:
            # 如果min和max相等，设置为0
            normalized_channel = np.zeros_like(channel_data)

        # 量化
        quantized_channel = np.round(normalized_channel * max_quant_value)

        # 放置量化后的通道数据
        packed_frame[start_h:end_h, start_w:end_w] = quantized_channel

        if i < 5:  # 只显示前5个通道的信息
            print(f"   通道 {i}: 位置({start_h}:{end_h}, {start_w}:{end_w}), min={channel_min:.6f}, max={channel_max:.6f}")

    print(f"\n📊 每通道量化结果:")
    print(f"   量化后范围: [{packed_frame.min():.0f}, {packed_frame.max():.0f}]")

    # 转换为合适的数据类型
    if bit_depth <= 8:
        quantized_frame = packed_frame.astype(np.uint8)
    else:
        quantized_frame = packed_frame.astype(np.uint16)

    # 确定输出路径
    if output_dir is None:
        output_dir = os.path.dirname(npy_path)

    os.makedirs(output_dir, exist_ok=True)

    # 生成输出文件名
    base_name = os.path.splitext(os.path.basename(npy_path))[0]
    yuv_filename = f"{base_name}_channel_quantized_frame{frame_idx}_{frame_width}x{frame_height}_{bit_depth}bit.yuv"
    yuv_path = os.path.join(output_dir, yuv_filename)

    # 保存YUV文件
    print(f"\n💾 保存每通道量化YUV文件: {yuv_path}")
    with open(yuv_path, 'wb') as f:
        quantized_frame.tofile(f)

    file_size = os.path.getsize(yuv_path)
    print(f"📊 YUV文件大小: {file_size} 字节")

    # 同时生成对应的图片文件
    img_filename = f"{base_name}_channel_quantized_frame{frame_idx}.png"
    img_path = os.path.join(output_dir, img_filename)

    print(f"🖼️ 生成对应图片: {img_path}")
    # 为了图片显示，使用归一化后的数据而不是量化后的数据
    normalized_display = packed_frame / max_quant_value  # 转换回[0,1]范围用于显示

    plt.figure(figsize=(12, 8))
    plt.imshow(normalized_display, cmap='gray', aspect='auto')
    plt.colorbar(label='Normalized Value (Per-Channel)')
    plt.title(f'{base_name} - Channel Quantization (Frame {frame_idx}) (Grayscale)\nShape: {frame_width}×{frame_height} ({C} channels)')
    plt.xlabel('Width')
    plt.ylabel('Height')
    plt.savefig(img_path, dpi=150, bbox_inches='tight')
    plt.close()

    img_size = os.path.getsize(img_path)
    print(f"📊 图片文件大小: {img_size} 字节")

    # 保存参数文件
    params_filename = f"{base_name}_channel_quantized_frame{frame_idx}_params.txt"
    params_path = os.path.join(output_dir, params_filename)

    with open(params_path, 'w') as f:
        f.write(f"# 每通道量化YUV格式参数\n")
        f.write(f"frame_index={frame_idx}\n")
        f.write(f"bit_depth={bit_depth}\n")
        f.write(f"frame_width={frame_width}\n")
        f.write(f"frame_height={frame_height}\n")
        f.write(f"original_shape={features.shape}\n")
        f.write(f"grid_layout={rows}x{cols}\n")
        f.write(f"channel_size={H}x{W}\n")
        f.write(f"channels_used={C}\n")
        f.write(f"minmax_file={minmax_path}\n")
        f.write(f"yuv_file={yuv_path}\n")
        f.write(f"image_file={img_path}\n")
        f.write(f"# 前5个通道的min/max值:\n")
        for i in range(min(5, C)):
            f.write(f"channel_{i}_min={frame_minmax[i, 0]:.8f}\n")
            f.write(f"channel_{i}_max={frame_minmax[i, 1]:.8f}\n")

    print(f"✅ 每通道量化转换完成!")
    print(f"📁 YUV文件: {yuv_path}")
    print(f"📁 图片文件: {img_path}")
    print(f"📁 参数文件: {params_path}")

    return yuv_path, img_path, params_path

def main():
    """主函数"""
    print("🎯 === NPY到YUV+图片转换工具 ===")

    # 默认文件路径
    base_path = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_feature_export/split-inference-video/fctm_original/SFUHW/sfu-hw-BasketballDrill_832x480_50_val/qp10/codec_output"
    encode_file = f"{base_path}/feature_export/encode_frame0_original.npy"
    decode_file = f"{base_path}/feature_export/decode_frame0_reconstructed.npy"
    minmax_file = f"{base_path}/sidecar_data/minmax.npy"

    # 输出目录
    output_dir = f"{base_path}/feature_export/yuv_and_images"

    print(f"📋 转换参数:")
    print(f"   布局方式: 网格排列")
    print(f"   格式: YUV 4:0:0 + PNG图片")
    print(f"   位深度: 10bit")
    print(f"   输出目录: {output_dir}")
    print(f"   MinMax文件: {minmax_file}")

    # 1. 转换编码端文件 - 全局量化
    print(f"\n1️⃣ 转换编码端原始特征 (全局量化)...")
    if os.path.exists(encode_file):
        convert_to_simple_yuv(encode_file, output_dir, bit_depth=10)
    else:
        print(f"❌ 编码端文件不存在: {encode_file}")

    # 2. 转换解码端文件 - 全局量化
    print(f"\n2️⃣ 转换解码端重建特征 (全局量化)...")
    if os.path.exists(decode_file):
        convert_to_simple_yuv(decode_file, output_dir, bit_depth=10)
    else:
        print(f"❌ 解码端文件不存在: {decode_file}")

    # 3. 转换编码端文件 - 每通道量化
    print(f"\n3️⃣ 转换编码端原始特征 (每通道量化)...")
    if os.path.exists(encode_file) and os.path.exists(minmax_file):
        convert_to_channel_quantized_yuv(encode_file, minmax_file, frame_idx=0, output_dir=output_dir, bit_depth=10)
    else:
        if not os.path.exists(encode_file):
            print(f"❌ 编码端文件不存在: {encode_file}")
        if not os.path.exists(minmax_file):
            print(f"❌ MinMax文件不存在: {minmax_file}")

    # 4. 转换解码端文件 - 每通道量化
    print(f"\n4️⃣ 转换解码端重建特征 (每通道量化)...")
    if os.path.exists(decode_file) and os.path.exists(minmax_file):
        convert_to_channel_quantized_yuv(decode_file, minmax_file, frame_idx=0, output_dir=output_dir, bit_depth=10)
    else:
        if not os.path.exists(decode_file):
            print(f"❌ 解码端文件不存在: {decode_file}")
        if not os.path.exists(minmax_file):
            print(f"❌ MinMax文件不存在: {minmax_file}")

    print(f"\n🎉 所有转换完成!")
    print(f"\n💡 生成的文件类型:")
    print(f"   1. 全局量化YUV + 图片: 使用所有通道的全局min/max进行归一化")
    print(f"   2. 每通道量化YUV + 图片: 使用每个通道独立的min/max进行归一化")
    print(f"\n💡 查看方式:")
    print(f"   📷 图片文件: 直接打开PNG文件即可查看")
    print(f"   🎬 YUV文件: ffplay -f rawvideo -pixel_format gray10le -video_size 336x224 file.yuv")
    print(f"\n📁 输出目录: {output_dir}")

if __name__ == "__main__":
    main()
