#!/usr/bin/env python3
"""
每个序列每个打包特征帧内所有通道Min/Max变化分析

修改分析：
1. 每个视频序列只保留一个QP（选择最小QP）
2. 统计每个序列中每个打包特征帧内所有通道的min和max变化
3. 分析帧内通道间的min/max分布，而不是跨帧的变化
4. 关注单个帧内所有通道的统计特性

支持新的3维minmax.npy格式: (N_frames, C, 2)
"""

import os
import numpy as np
import matplotlib.pyplot as plt
import glob

def load_sequences_data():
    """加载所有序列的min/max数据，每个序列只保留一个QP"""
    print("🔍 正在加载所有序列的min/max数据...")

    # 查找所有minmax.npy文件
    base_dir = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_signaling_backup/split-inference-video"
    pattern = os.path.join(base_dir, "**", "sidecar_data", "minmax.npy")
    minmax_files = glob.glob(pattern, recursive=True)

    print(f"📁 找到 {len(minmax_files)} 个minmax文件")

    # 用字典来存储每个序列的数据，key是序列名，value是QP数据列表
    sequence_dict = {}

    for file_path in minmax_files:
        try:
            # 提取序列名和QP
            path_parts = file_path.split(os.sep)
            seq_name = "unknown"
            qp = 0

            for part in path_parts:
                if 'sfu-hw-' in part:
                    seq_name = part.replace('sfu-hw-', '')
                elif part.startswith('qp'):
                    qp = int(part[2:])

            # 加载数据
            data = np.load(file_path)
            print(f"📊 {seq_name} QP{qp}: {data.shape}")

            if data.ndim == 3:
                # 新格式 (N_frames, C, 2)
                n_frames, n_channels, _ = data.shape
                seq_data = {
                    'name': seq_name,
                    'qp': qp,
                    'data': data,
                    'n_frames': n_frames,
                    'n_channels': n_channels,
                    'file_path': file_path
                }
            elif data.ndim == 2:
                # 旧格式兼容 (C, 2)
                seq_data = {
                    'name': seq_name,
                    'qp': qp,
                    'data': data[np.newaxis, :, :],  # 转换为3D
                    'n_frames': 1,
                    'n_channels': data.shape[0],
                    'file_path': file_path
                }
            else:
                continue

            # 将数据添加到字典中
            if seq_name not in sequence_dict:
                sequence_dict[seq_name] = []
            sequence_dict[seq_name].append(seq_data)

        except Exception as e:
            print(f"❌ 加载失败 {file_path}: {e}")
            continue

    # 保留所有QP
    sequence_info = []
    for seq_name, qp_list in sequence_dict.items():
        if qp_list:
            # 按QP排序，保留所有QP
            qp_list.sort(key=lambda x: x['qp'])
            sequence_info.extend(qp_list)  # 添加所有QP
            qp_values = [seq['qp'] for seq in qp_list]
            print(f"✅ 序列 {seq_name}: QP{qp_values} (共有 {len(qp_list)} 个QP)")

    print(f"✅ 成功加载 {len(sequence_info)} 个序列-QP组合")

    return sequence_info

def analyze_sequence_internal_variation(sequence_info, output_dir="analysis_results"):
    """分析每个序列中每个打包特征帧内所有通道的min/max变化"""
    print(f"📊 分析每个序列中所有打包特征帧内所有通道的min/max变化...")

    os.makedirs(output_dir, exist_ok=True)

    # 设置matplotlib支持更好的显示
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['figure.facecolor'] = 'white'

    # 为每个序列创建分析
    for seq_idx, seq in enumerate(sequence_info):
        print(f"🔍 分析序列: {seq['name']} QP{seq['qp']}")

        data = seq['data']  # (N_frames, C, 2)
        n_frames, n_channels, _ = data.shape

        # 分析所有帧
        analyze_frames = n_frames
        print(f"   📊 分析所有{analyze_frames}帧（{n_channels}通道）")

        # 只取前几帧进行分析
        frame_channel_mins = data[:analyze_frames, :, 0]  # (analyze_frames, C) - 每帧每通道的min值
        frame_channel_maxs = data[:analyze_frames, :, 1]  # (analyze_frames, C) - 每帧每通道的max值

        # 对于每一帧，计算该帧内所有通道的min和max的统计
        frame_min_stats = []  # 存储每帧内所有通道min值的统计
        frame_max_stats = []  # 存储每帧内所有通道max值的统计

        for frame_idx in range(analyze_frames):
            # 当前帧所有通道的min值
            current_frame_mins = frame_channel_mins[frame_idx, :]  # (C,)
            # 当前帧所有通道的max值
            current_frame_maxs = frame_channel_maxs[frame_idx, :]  # (C,)

            # 计算当前帧内所有通道的统计
            frame_min_stats.append({
                'frame_idx': frame_idx,
                'min_mean': np.mean(current_frame_mins),
                'min_std': np.std(current_frame_mins),
                'min_min': np.min(current_frame_mins),
                'min_max': np.max(current_frame_mins),
                'all_mins': current_frame_mins
            })

            frame_max_stats.append({
                'frame_idx': frame_idx,
                'max_mean': np.mean(current_frame_maxs),
                'max_std': np.std(current_frame_maxs),
                'max_min': np.min(current_frame_maxs),
                'max_max': np.max(current_frame_maxs),
                'all_maxs': current_frame_maxs
            })

        # 为每一帧创建一个子图，显示通道索引 vs min/max值
        # 计算合理的子图布局
        if analyze_frames <= 10:
            # 少于等于10帧：使用2行布局
            cols = min(5, analyze_frames)
            rows = (analyze_frames + cols - 1) // cols  # 向上取整
        elif analyze_frames <= 50:
            # 11-50帧：使用10列布局
            cols = 10
            rows = (analyze_frames + cols - 1) // cols
        else:
            # 超过50帧：使用15列布局
            cols = 15
            rows = (analyze_frames + cols - 1) // cols

        # 设置图片大小，但限制最大尺寸
        fig_width = min(cols * 4, 60)  # 最大宽度60英寸
        fig_height = min(rows * 3, 100)  # 最大高度100英寸
        figsize = (fig_width, fig_height)

        fig, axes = plt.subplots(rows, cols, figsize=figsize)

        # 确保axes是二维数组
        if rows == 1 and cols == 1:
            axes = np.array([[axes]])
        elif rows == 1:
            axes = axes.reshape(1, -1)
        elif cols == 1:
            axes = axes.reshape(-1, 1)

        # 通道索引
        channel_indices = np.arange(n_channels)

        # 为每一帧创建图表
        for frame_idx in range(analyze_frames):
            row = frame_idx // cols
            col = frame_idx % cols
            ax = axes[row, col]

            # 获取当前帧的min和max值
            current_frame_mins = frame_channel_mins[frame_idx, :]  # (C,)
            current_frame_maxs = frame_channel_maxs[frame_idx, :]  # (C,)

            # 绘制每个通道的min和max值
            ax.plot(channel_indices, current_frame_mins, 'b-', linewidth=0.8, alpha=0.7, label='Min')
            ax.plot(channel_indices, current_frame_maxs, 'r-', linewidth=0.8, alpha=0.7, label='Max')

            # 添加均值线
            frame_min_mean = np.mean(current_frame_mins)
            frame_max_mean = np.mean(current_frame_maxs)
            ax.axhline(y=frame_min_mean, color='blue', linestyle='--', linewidth=1.5, alpha=0.8)
            ax.axhline(y=frame_max_mean, color='red', linestyle='--', linewidth=1.5, alpha=0.8)

            ax.set_xlabel('Channel', fontsize=8)
            ax.set_ylabel('Min/Max', fontsize=8)
            ax.set_title(f'Frame {frame_idx}', fontsize=9, fontweight='bold')
            ax.grid(True, alpha=0.3)
            ax.legend(fontsize=6)

            # 设置x轴范围和刻度
            ax.set_xlim(0, n_channels-1)
            # 减少x轴刻度数量
            if n_channels > 50:
                ax.set_xticks(np.arange(0, n_channels, n_channels//5))

        # 隐藏多余的子图
        for frame_idx in range(analyze_frames, rows * cols):
            row = frame_idx // cols
            col = frame_idx % cols
            axes[row, col].set_visible(False)

        plt.tight_layout()

        # 保存图片
        output_path = os.path.join(output_dir, f'sequence_internal_analysis_{seq["name"]}_QP{seq["qp"]}.png')
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"💾 序列分析已保存: {output_path}")

        # 计算统计信息
        all_frame_mins = frame_channel_mins.flatten()  # 所有帧所有通道的min值
        all_frame_maxs = frame_channel_maxs.flatten()  # 所有帧所有通道的max值
        overall_min_mean = np.mean(all_frame_mins)
        overall_max_mean = np.mean(all_frame_maxs)
        frame_min_means = [stat['min_mean'] for stat in frame_min_stats]
        frame_max_means = [stat['max_mean'] for stat in frame_max_stats]

        # 打印统计信息
        print(f"   📊 序列内统计（所有{analyze_frames}帧）:")
        print(f"      帧数: {analyze_frames}, 通道数: {n_channels}")
        print(f"      Min均值: {overall_min_mean:.4f}, 标准差: {np.std(all_frame_mins):.4f}")
        print(f"      Max均值: {overall_max_mean:.4f}, 标准差: {np.std(all_frame_maxs):.4f}")
        print(f"      Min范围: [{np.min(all_frame_mins):.4f}, {np.max(all_frame_mins):.4f}]")
        print(f"      Max范围: [{np.min(all_frame_maxs):.4f}, {np.max(all_frame_maxs):.4f}]")
        print(f"      每帧Min均值范围: [{np.min(frame_min_means):.4f}, {np.max(frame_min_means):.4f}]")
        print(f"      每帧Max均值范围: [{np.min(frame_max_means):.4f}, {np.max(frame_max_means):.4f}]")

        # 只打印前5帧和后5帧的详细统计（如果帧数较多）
        if analyze_frames <= 10:
            print(f"   📋 每帧详细统计:")
            for i, (min_stat, max_stat) in enumerate(zip(frame_min_stats, frame_max_stats)):
                print(f"      帧{i}: Min均值={min_stat['min_mean']:.4f}, Max均值={max_stat['max_mean']:.4f}")
        else:
            print(f"   📋 前5帧和后5帧详细统计:")
            # 前5帧
            for i in range(5):
                min_stat, max_stat = frame_min_stats[i], frame_max_stats[i]
                print(f"      帧{i}: Min均值={min_stat['min_mean']:.4f}, Max均值={max_stat['max_mean']:.4f}")
            print(f"      ... (省略中间{analyze_frames-10}帧)")
            # 后5帧
            for i in range(analyze_frames-5, analyze_frames):
                min_stat, max_stat = frame_min_stats[i], frame_max_stats[i]
                print(f"      帧{i}: Min均值={min_stat['min_mean']:.4f}, Max均值={max_stat['max_mean']:.4f}")
        print()

    return output_dir

def create_sequence_comparison_summary(sequence_info, output_dir="analysis_results"):
    """创建序列间比较的汇总图"""
    print("📈 创建序列比较汇总图...")

    os.makedirs(output_dir, exist_ok=True)

    # 设置matplotlib支持更好的显示
    plt.rcParams['font.family'] = 'DejaVu Sans'
    plt.rcParams['axes.unicode_minus'] = False
    plt.rcParams['figure.facecolor'] = 'white'

    # 收集每个序列的统计信息
    seq_names = []
    seq_min_means = []
    seq_max_means = []
    seq_min_stds = []
    seq_max_stds = []
    seq_ranges = []

    for seq in sequence_info:
        data = seq['data']  # (N_frames, C, 2)

        # 获取所有帧所有通道的min和max值
        all_mins = data[:, :, 0].flatten()  # 所有min值
        all_maxs = data[:, :, 1].flatten()  # 所有max值

        seq_names.append(f"{seq['name']}_QP{seq['qp']}")
        seq_min_means.append(np.mean(all_mins))
        seq_max_means.append(np.mean(all_maxs))
        seq_min_stds.append(np.std(all_mins))
        seq_max_stds.append(np.std(all_maxs))
        seq_ranges.append(np.max(all_maxs) - np.min(all_mins))

    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(20, 12))

    x_pos = np.arange(len(seq_names))

    # 1. 序列间min/max均值比较
    width = 0.35
    ax1.bar(x_pos - width/2, seq_min_means, width, label='Min Mean', alpha=0.7, color='skyblue')
    ax1.bar(x_pos + width/2, seq_max_means, width, label='Max Mean', alpha=0.7, color='lightcoral')
    ax1.set_xlabel('Sequences', fontsize=12, fontweight='bold')
    ax1.set_ylabel('Mean Values', fontsize=12, fontweight='bold')
    ax1.set_title('Sequence-wise Min/Max Mean Comparison', fontsize=12, fontweight='bold')
    ax1.set_xticks(x_pos)
    ax1.set_xticklabels(seq_names, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 序列间标准差比较
    ax2.bar(x_pos - width/2, seq_min_stds, width, label='Min Std', alpha=0.7, color='blue')
    ax2.bar(x_pos + width/2, seq_max_stds, width, label='Max Std', alpha=0.7, color='red')
    ax2.set_xlabel('Sequences', fontsize=12, fontweight='bold')
    ax2.set_ylabel('Standard Deviation', fontsize=12, fontweight='bold')
    ax2.set_title('Sequence-wise Min/Max Standard Deviation', fontsize=12, fontweight='bold')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(seq_names, rotation=45, ha='right')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 序列动态范围比较
    ax3.bar(x_pos, seq_ranges, alpha=0.7, color='green')
    ax3.set_xlabel('Sequences', fontsize=12, fontweight='bold')
    ax3.set_ylabel('Dynamic Range (Max - Min)', fontsize=12, fontweight='bold')
    ax3.set_title('Sequence-wise Dynamic Range', fontsize=12, fontweight='bold')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels(seq_names, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)

    # 4. 散点图：标准差 vs 均值
    ax4.scatter(seq_min_means, seq_min_stds, c='blue', s=100, alpha=0.7, label='Min')
    ax4.scatter(seq_max_means, seq_max_stds, c='red', s=100, alpha=0.7, label='Max')

    # 添加序列标签
    for i, name in enumerate(seq_names):
        ax4.annotate(name, (seq_min_means[i], seq_min_stds[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.8)
        ax4.annotate(name, (seq_max_means[i], seq_max_stds[i]),
                    xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.8)

    ax4.set_xlabel('Mean Value', fontsize=12, fontweight='bold')
    ax4.set_ylabel('Standard Deviation', fontsize=12, fontweight='bold')
    ax4.set_title('Mean vs Standard Deviation Scatter', fontsize=12, fontweight='bold')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()

    # 保存图片
    output_path = os.path.join(output_dir, 'sequence_comparison_summary.png')
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    print(f"💾 序列比较汇总图已保存: {output_path}")

    return output_path

def main():
    """主函数"""
    print("🎯 === 每个序列每个打包特征帧内所有通道Min/Max变化分析 ===")
    print("📋 分析内容:")
    print("   1. 保留所有QP的分析结果")
    print("   2. 统计每个序列中每个打包特征帧内所有通道的min和max变化")
    print("   3. 分析帧内通道间的min/max分布和变化")
    print()

    # 切换到正确目录
    os.chdir('/work/Users/<USER>/fctm-v7.0/scripts/evaluation')

    # 加载数据
    sequence_info = load_sequences_data()

    if len(sequence_info) == 0:
        print("❌ 没有找到数据！")
        return

    # 创建分析图表
    print("\n1️⃣ 分析每个序列中每个打包特征帧内所有通道的min/max变化...")
    analyze_sequence_internal_variation(sequence_info)

    # 不再生成序列间比较汇总图

    print("\n🎉 === 分析完成 ===")
    print("📁 结果保存在: analysis_results/")
    print("📄 生成的文件:")
    print("   - sequence_internal_analysis_[序列名]_QP[QP值].png (每个序列-QP组合一个)")
    print()
    print("💡 现在的分析关注:")
    print("   ✅ 保留所有QP的分析结果")
    print("   ✅ 分析所有打包特征帧内所有通道的min和max分布")
    print("   ✅ 帧内通道间的统计变化，而不是跨帧统计")
    print("   ✅ 每帧内通道min/max的均值变化趋势")
    print("   ✅ 横坐标是通道索引，纵坐标是min/max值")

if __name__ == "__main__":
    main()
