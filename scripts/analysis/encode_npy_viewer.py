#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细NPY文件查看器

专门用于查看FCTM导出的6个npy文件的详细数值
"""

import numpy as np
import os
import sys

def view_original_tensor(file_path):
    """查看原始特征张量"""
    data = np.load(file_path)
    print("🔍 原始特征张量详细信息:")
    print(f"  形状: {data.shape} (通道数, 高度, 宽度)")
    print(f"  数据类型: {data.dtype}")
    print(f"  数值范围: [{data.min():.6f}, {data.max():.6f}]")
    print(f"  统计: 均值={data.mean():.6f}, 标准差={data.std():.6f}")

    print("\n  前5个通道的所有数值:")
    for c in range(min(5, data.shape[0])):
        print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
        channel_data = data[c]
        for h in range(channel_data.shape[0]):
            row_str = "      " + " ".join([f"{val:8.4f}" for val in channel_data[h]])
            print(row_str)
        print()

    if data.shape[0] > 5:
        print(f"  后5个通道的所有数值:")
        for c in range(data.shape[0]-5, data.shape[0]):
            print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
            channel_data = data[c]
            for h in range(channel_data.shape[0]):
                row_str = "      " + " ".join([f"{val:8.4f}" for val in channel_data[h]])
                print(row_str)
            print()

    # 显示极值通道
    channel_mins = np.min(data, axis=(1,2))
    channel_maxs = np.max(data, axis=(1,2))
    min_channel = np.argmin(channel_mins)
    max_channel = np.argmax(channel_maxs)

    print(f"  极值通道:")
    print(f"    最小值通道 {min_channel}: {channel_mins[min_channel]:.6f}")
    print(f"    最大值通道 {max_channel}: {channel_maxs[max_channel]:.6f}")

def view_normalized_tensor(file_path):
    """查看归一化张量"""
    data = np.load(file_path)
    print("🔍 归一化张量详细信息:")
    print(f"  形状: {data.shape}")
    print(f"  数值范围: [{data.min():.6f}, {data.max():.6f}] (应该是[0,1])")
    print(f"  统计: 均值={data.mean():.6f}, 标准差={data.std():.6f}")

    # 检查是否正确归一化
    if data.min() >= 0 and data.max() <= 1:
        print("  ✅ 归一化正确: 数值在[0,1]范围内")
    else:
        print("  ❌ 归一化异常: 数值超出[0,1]范围")

    print("\n  前5个通道的所有数值:")
    for c in range(min(5, data.shape[0])):
        print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
        channel_data = data[c]
        for h in range(channel_data.shape[0]):
            row_str = "      " + " ".join([f"{val:8.4f}" for val in channel_data[h]])
            print(row_str)
        print()

    if data.shape[0] > 5:
        print(f"  后5个通道的所有数值:")
        for c in range(data.shape[0]-5, data.shape[0]):
            print(f"    通道 {c} (统计: min={data[c].min():.4f}, max={data[c].max():.4f}, mean={data[c].mean():.4f}):")
            channel_data = data[c]
            for h in range(channel_data.shape[0]):
                row_str = "      " + " ".join([f"{val:8.4f}" for val in channel_data[h]])
                print(row_str)
            print()

def view_quantized_tensor(file_path):
    """查看量化张量"""
    data = np.load(file_path)
    print("🔍 量化张量详细信息:")
    print(f"  形状: {data.shape}")
    print(f"  数值范围: [{data.min():.0f}, {data.max():.0f}] (应该是[0,1023])")
    print(f"  统计: 均值={data.mean():.2f}, 标准差={data.std():.2f}")

    # 检查量化级别
    unique_values = np.unique(data)
    print(f"  唯一值数量: {len(unique_values)} (最多1024个)")
    print(f"  前10个量化值: {unique_values[:10]}")

    # 检查量化分布
    hist, _ = np.histogram(data, bins=50)
    print(f"  量化分布 (前10个区间的频次): {hist[:10]}")

    print("\n  前5个通道的所有数值:")
    for c in range(min(5, data.shape[0])):
        print(f"    通道 {c} (统计: min={data[c].min():.0f}, max={data[c].max():.0f}, mean={data[c].mean():.2f}):")
        channel_data = data[c]
        for h in range(channel_data.shape[0]):
            row_str = "      " + " ".join([f"{val:6.0f}" for val in channel_data[h]])
            print(row_str)
        print()

    if data.shape[0] > 5:
        print(f"  后5个通道的所有数值:")
        for c in range(data.shape[0]-5, data.shape[0]):
            print(f"    通道 {c} (统计: min={data[c].min():.0f}, max={data[c].max():.0f}, mean={data[c].mean():.2f}):")
            channel_data = data[c]
            for h in range(channel_data.shape[0]):
                row_str = "      " + " ".join([f"{val:6.0f}" for val in channel_data[h]])
                print(row_str)
            print()

def view_packed_frame(file_path):
    """查看打包帧"""
    data = np.load(file_path)
    print("🔍 打包帧详细信息:")
    print(f"  形状: {data.shape} (帧高度, 帧宽度)")
    print(f"  数值范围: [{data.min():.0f}, {data.max():.0f}]")
    print(f"  统计: 均值={data.mean():.2f}, 标准差={data.std():.2f}")

    print(f"\n  前16行的所有数值:")
    for h in range(min(16, data.shape[0])):
        row_str = f"    行{h:2d}: " + " ".join([f"{val:6.0f}" for val in data[h]])
        print(row_str)

    if data.shape[0] > 16:
        print(f"\n  后16行的所有数值:")
        for h in range(data.shape[0]-16, data.shape[0]):
            row_str = f"    行{h:2d}: " + " ".join([f"{val:6.0f}" for val in data[h]])
            print(row_str)

def view_normalization_params(file_path):
    """查看归一化参数"""
    data = np.load(file_path)
    print("🔍 归一化参数详细信息:")
    print(f"  形状: {data.shape} (通道数, 2)")
    print(f"  每行格式: [min_value, max_value]")
    
    min_vals = data[:, 0]
    max_vals = data[:, 1]
    ranges = max_vals - min_vals
    
    print(f"\n  统计摘要:")
    print(f"    最小值范围: [{min_vals.min():.6f}, {min_vals.max():.6f}]")
    print(f"    最大值范围: [{max_vals.min():.6f}, {max_vals.max():.6f}]")
    print(f"    动态范围: [{ranges.min():.6f}, {ranges.max():.6f}]")
    print(f"    平均动态范围: {ranges.mean():.6f} ± {ranges.std():.6f}")
    
    print(f"\n  前10个通道的参数:")
    print(f"    {'通道':<4} {'最小值':<12} {'最大值':<12} {'动态范围':<12}")
    print(f"    {'-'*44}")
    for i in range(min(10, len(data))):
        min_val, max_val = data[i]
        dynamic_range = max_val - min_val
        print(f"    {i:<4} {min_val:<12.6f} {max_val:<12.6f} {dynamic_range:<12.6f}")
    
    # 找出极值通道
    min_range_idx = np.argmin(ranges)
    max_range_idx = np.argmax(ranges)
    print(f"\n  极值通道:")
    print(f"    最小动态范围: 通道{min_range_idx} = {ranges[min_range_idx]:.6f}")
    print(f"    最大动态范围: 通道{max_range_idx} = {ranges[max_range_idx]:.6f}")

def view_group_analysis(file_path):
    """查看分组分析"""
    try:
        data = np.load(file_path, allow_pickle=True).item()
        print("🔍 分组分析详细信息:")
        print(f"  数据类型: {type(data)}")
        print(f"  主要键: {list(data.keys())}")
        
        print(f"\n  基本统计:")
        print(f"    动态范围均值 (μ): {data['mean_range']:.6f}")
        print(f"    动态范围标准差 (σ): {data['std_range']:.6f}")
        
        print(f"\n  分组统计:")
        group_stats = data['group_stats']
        total_channels = sum(info['count'] for info in group_stats.values())
        
        print(f"    总通道数: {total_channels}")
        print(f"    {'分组名称':<15} {'通道数':<8} {'占比':<8} {'全局最小值':<12} {'全局最大值':<12}")
        print(f"    {'-'*65}")
        
        for group_name, info in group_stats.items():
            count = info['count']
            percentage = (count / total_channels * 100) if total_channels > 0 else 0
            global_min = info['global_min']
            global_max = info['global_max']
            print(f"    {group_name:<15} {count:<8} {percentage:<7.1f}% {global_min:<12.4f} {global_max:<12.4f}")
        
        print(f"\n  通道分配示例:")
        channel_to_group = data['channel_to_group']
        for ch, group in list(channel_to_group.items())[:10]:
            print(f"    通道{ch} -> {group}")
        if len(channel_to_group) > 10:
            print(f"    ... (共{len(channel_to_group)}个通道)")
            
    except Exception as e:
        print(f"❌ 读取分组分析文件失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python detailed_npy_viewer.py <frame_folder_path>")
        print("例如: python detailed_npy_viewer.py /path/to/frame_0000")
        return

    frame_folder = sys.argv[1]

    if not os.path.exists(frame_folder):
        print(f"❌ 文件夹不存在: {frame_folder}")
        return

    # 创建输出txt文件
    output_txt = os.path.join(frame_folder, "detailed_npy_analysis.txt")

    print("=" * 80)
    print(f"详细NPY文件查看器 - {os.path.basename(frame_folder)}")
    print("=" * 80)
    print(f"输出将保存到: {output_txt}")

    # 6个文件的查看函数映射
    file_viewers = {
        "original_tensor.npy": view_original_tensor,
        "normalized_tensor.npy": view_normalized_tensor,
        "quantized_tensor.npy": view_quantized_tensor,
        "packed_frame.npy": view_packed_frame,
        "normalization_params.npy": view_normalization_params,
        "group_analysis.npy": view_group_analysis,
    }

    # 重定向输出到文件
    import io
    from contextlib import redirect_stdout

    output_buffer = io.StringIO()

    with redirect_stdout(output_buffer):
        print("=" * 80)
        print(f"详细NPY文件查看器 - {os.path.basename(frame_folder)}")
        print("=" * 80)
        print(f"生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"源文件夹: {frame_folder}")
        print()

        for filename, viewer_func in file_viewers.items():
            file_path = os.path.join(frame_folder, filename)

            print(f"\n{'='*60}")
            print(f"📁 {filename}")
            print(f"{'='*60}")

            if os.path.exists(file_path):
                try:
                    viewer_func(file_path)
                except Exception as e:
                    print(f"❌ 处理文件时出错: {e}")
            else:
                print(f"❌ 文件不存在: {file_path}")

        print(f"\n{'='*80}")
        print("✅ 详细查看完成!")
        print(f"{'='*80}")

    # 保存到txt文件
    with open(output_txt, 'w', encoding='utf-8') as f:
        f.write(output_buffer.getvalue())

    # 同时在控制台显示
    print(output_buffer.getvalue())

    # 显示文件信息
    file_size = os.path.getsize(output_txt) / 1024  # KB
    print(f"\n✅ 分析结果已保存到: {output_txt}")
    print(f"📊 文件大小: {file_size:.1f} KB")

if __name__ == "__main__":
    main()
