#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
编码端和解码端数据对比分析器

同时分析编码端和解码端的npy文件，进行详细对比
"""

import numpy as np
import os
import sys
from datetime import datetime

def compare_packed_frames(encode_packed, decode_packed):
    """对比编码端和解码端的打包帧"""
    print("🔄 打包帧对比分析:")
    print(f"  编码端形状: {encode_packed.shape}")
    print(f"  解码端形状: {decode_packed.shape}")
    
    if encode_packed.shape != decode_packed.shape:
        print("  ❌ 形状不匹配!")
        return
    
    # 计算差异
    diff = np.abs(encode_packed - decode_packed)
    max_diff = diff.max()
    mean_diff = diff.mean()
    
    print(f"  数值差异:")
    print(f"    最大差异: {max_diff:.8f}")
    print(f"    平均差异: {mean_diff:.8f}")
    print(f"    差异标准差: {diff.std():.8f}")
    
    if max_diff < 1e-6:
        print("  ✅ 打包帧完全一致")
    else:
        print("  ⚠️  打包帧存在差异")
        
        # 显示差异最大的位置
        max_diff_pos = np.unravel_index(np.argmax(diff), diff.shape)
        print(f"    最大差异位置: {max_diff_pos}")
        print(f"    编码端值: {encode_packed[max_diff_pos]:.6f}")
        print(f"    解码端值: {decode_packed[max_diff_pos]:.6f}")

def compare_quantization_process(encode_quantized, decode_dequantized):
    """对比量化和逆量化过程"""
    print("🔄 量化/逆量化过程对比:")
    print(f"  编码端量化张量形状: {encode_quantized.shape}")
    print(f"  解码端逆量化张量形状: {decode_dequantized.shape}")

    # 检查形状是否匹配
    if encode_quantized.shape != decode_dequantized.shape:
        print("  ⚠️  形状不匹配，无法直接对比量化/逆量化过程")
        print("  说明: 编码端是3D张量(C,H,W)，解码端是2D打包帧(H,W)")
        return

    # 验证逆量化是否正确
    # 逆量化公式: quantized / 1023.0 (假设10位量化)
    expected_dequantized = encode_quantized / 1023.0
    diff = np.abs(expected_dequantized - decode_dequantized)

    print(f"  逆量化验证:")
    print(f"    最大差异: {diff.max():.8f}")
    print(f"    平均差异: {diff.mean():.8f}")

    if diff.max() < 1e-6:
        print("  ✅ 逆量化过程完全正确")
    else:
        print("  ⚠️  逆量化过程存在差异")

def compare_reconstruction_quality(encode_original, decode_reconstructed):
    """对比重建质量"""
    print("🔄 重建质量对比:")
    print(f"  原始张量形状: {encode_original.shape}")
    print(f"  重建张量形状: {decode_reconstructed.shape}")

    # 处理不同的维度情况
    if len(decode_reconstructed.shape) == 4 and decode_reconstructed.shape[0] == 1:
        # 解码端形状是 (1, C, H, W)，去掉第一个维度
        decode_reconstructed = decode_reconstructed[0]
        print(f"  调整后重建张量形状: {decode_reconstructed.shape}")

    if encode_original.shape != decode_reconstructed.shape:
        print("  ❌ 形状仍然不匹配!")
        return
    
    # 计算重建误差
    reconstruction_error = np.abs(encode_original - decode_reconstructed)
    max_error = reconstruction_error.max()
    mean_error = reconstruction_error.mean()
    
    print(f"  重建误差分析:")
    print(f"    最大重建误差: {max_error:.6f}")
    print(f"    平均重建误差: {mean_error:.6f}")
    print(f"    误差标准差: {reconstruction_error.std():.6f}")
    
    # 计算PSNR
    mse = np.mean((encode_original - decode_reconstructed) ** 2)
    if mse > 0:
        max_val = max(np.abs(encode_original).max(), np.abs(decode_reconstructed).max())
        psnr = 20 * np.log10(max_val) - 10 * np.log10(mse)
        print(f"    重建PSNR: {psnr:.2f} dB")
    else:
        print("    重建PSNR: ∞ dB (完美重建)")
    
    # 按通道分析重建误差
    print(f"  前10个通道的重建误差:")
    for c in range(min(10, encode_original.shape[0])):
        ch_error = np.mean(np.abs(encode_original[c] - decode_reconstructed[c]))
        ch_max_error = np.max(np.abs(encode_original[c] - decode_reconstructed[c]))
        print(f"    通道{c:2d}: 平均误差={ch_error:.6f}, 最大误差={ch_max_error:.6f}")

def analyze_data_flow(encode_folder, decode_folder):
    """分析完整的数据流转过程"""
    print("🔄 完整数据流转分析:")
    
    # 加载编码端数据
    encode_files = {
        'original': os.path.join(encode_folder, 'original_tensor.npy'),
        'normalized': os.path.join(encode_folder, 'normalized_tensor.npy'),
        'quantized': os.path.join(encode_folder, 'quantized_tensor.npy'),
        'packed': os.path.join(encode_folder, 'packed_frame.npy'),
    }
    
    # 加载解码端数据
    decode_files = {
        'packed': os.path.join(decode_folder, 'decoded_packed_frame.npy'),
        'dequantized': os.path.join(decode_folder, 'dequantized_tensor.npy'),
        'unpacked': os.path.join(decode_folder, 'unpacked_tensor.npy'),
        'reconstructed': os.path.join(decode_folder, 'reconstructed_tensor.npy'),
    }
    
    # 检查文件存在性
    encode_data = {}
    decode_data = {}
    
    for key, path in encode_files.items():
        if os.path.exists(path):
            encode_data[key] = np.load(path)
        else:
            print(f"  ⚠️  编码端文件缺失: {os.path.basename(path)}")
    
    for key, path in decode_files.items():
        if os.path.exists(path):
            decode_data[key] = np.load(path)
        else:
            print(f"  ⚠️  解码端文件缺失: {os.path.basename(path)}")
    
    print(f"  数据流转路径:")
    print(f"    编码: 原始 → 归一化 → 量化 → 打包")
    print(f"    解码: 打包 → 逆量化 → 逆打包 → 重建")
    print()
    
    # 执行各项对比
    if 'packed' in encode_data and 'packed' in decode_data:
        compare_packed_frames(encode_data['packed'], decode_data['packed'])
        print()
    
    if 'quantized' in encode_data and 'dequantized' in decode_data:
        compare_quantization_process(encode_data['quantized'], decode_data['dequantized'])
        print()
    
    if 'original' in encode_data and 'reconstructed' in decode_data:
        compare_reconstruction_quality(encode_data['original'], decode_data['reconstructed'])
        print()

def main():
    """主函数"""
    if len(sys.argv) != 3:
        print("用法: python encode_decode_compare.py <encode_frame_folder> <decode_frame_folder>")
        print()
        print("参数说明:")
        print("  encode_frame_folder: 编码端帧文件夹路径")
        print("  decode_frame_folder: 解码端帧文件夹路径")
        print()
        print("示例:")
        print("  python encode_decode_compare.py \\")
        print("    /path/to/exported_real_data/frame_0000 \\")
        print("    /path/to/exported_decode_data/frame_0000")
        return
    
    encode_folder = sys.argv[1]
    decode_folder = sys.argv[2]
    
    if not os.path.exists(encode_folder):
        print(f"❌ 编码端文件夹不存在: {encode_folder}")
        return
    
    if not os.path.exists(decode_folder):
        print(f"❌ 解码端文件夹不存在: {decode_folder}")
        return
    
    # 创建输出txt文件
    output_txt = os.path.join(os.path.dirname(decode_folder), "encode_decode_comparison.txt")
    
    print("=" * 80)
    print("编码端和解码端数据对比分析")
    print("=" * 80)
    print(f"编码端文件夹: {encode_folder}")
    print(f"解码端文件夹: {decode_folder}")
    print(f"输出将保存到: {output_txt}")
    print()
    
    # 重定向输出到文件
    import io
    from contextlib import redirect_stdout
    
    output_buffer = io.StringIO()
    
    with redirect_stdout(output_buffer):
        print("=" * 80)
        print("FCTM编码端和解码端数据对比分析")
        print("=" * 80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"编码端文件夹: {encode_folder}")
        print(f"解码端文件夹: {decode_folder}")
        print()
        
        # 执行完整分析
        analyze_data_flow(encode_folder, decode_folder)
        
        print("=" * 80)
        print("✅ 编码端和解码端对比分析完成!")
        print("=" * 80)
    
    # 保存到txt文件
    with open(output_txt, 'w', encoding='utf-8') as f:
        f.write(output_buffer.getvalue())
    
    # 同时在控制台显示
    print(output_buffer.getvalue())
    
    # 显示文件信息
    file_size = os.path.getsize(output_txt) / 1024  # KB
    print(f"\n✅ 对比分析结果已保存到: {output_txt}")
    print(f"📊 文件大小: {file_size:.1f} KB")

if __name__ == "__main__":
    main()
