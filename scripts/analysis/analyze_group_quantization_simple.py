#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分组量化结果分析脚本 - 简化版

用于汇总各个序列不同QP的npy统计信息
"""

import os
import sys
import glob
import numpy as np
import argparse
from collections import defaultdict

def load_group_info_files(base_dir):
    """
    加载所有分组信息文件
    
    参数:
    base_dir: 基础输出目录
    
    返回:
    dict: {(seq_name, qp): group_data}
    """
    group_files = glob.glob(f"{base_dir}/**/group_info.npy", recursive=True)
    print(f"找到 {len(group_files)} 个分组信息文件")
    
    results = {}
    
    for file_path in group_files:
        try:
            # 从路径中提取序列名和QP
            path_parts = file_path.split('/')
            seq_info = None
            qp_info = None
            
            for part in path_parts:
                if part.startswith('sfu-hw-'):
                    seq_info = part.replace('sfu-hw-', '')
                elif part.startswith('qp'):
                    qp_info = int(part.replace('qp', ''))
            
            if seq_info and qp_info is not None:
                # 加载数据
                data = np.load(file_path, allow_pickle=True).item()
                results[(seq_info, qp_info)] = data
                print(f"加载: {seq_info} QP{qp_info} - {len(data['frames'])} 帧")
            
        except Exception as e:
            print(f"加载文件失败 {file_path}: {e}")
    
    return results

def analyze_group_distribution(group_data):
    """
    分析分组分布统计
    """
    print("\n" + "=" * 80)
    print("分组分布统计分析")
    print("=" * 80)

    # 按序列和QP分组显示详细信息
    for (seq_name, qp), data in sorted(group_data.items()):
        print(f"\n📊 序列: {seq_name}, QP: {qp}")
        print("-" * 60)

        frame_count = len(data['frames'])
        print(f"总帧数: {frame_count}")

        if frame_count > 0:
            # 计算平均统计信息
            mean_ranges = [frame['mean_range'] for frame in data['frames']]
            std_ranges = [frame['std_range'] for frame in data['frames']]

            print(f"动态范围均值: {np.mean(mean_ranges):.4f} ± {np.std(mean_ranges):.4f}")
            print(f"动态范围标准差: {np.mean(std_ranges):.4f} ± {np.std(std_ranges):.4f}")

            # 显示每一帧的详细分组情况
            print("\n每帧分组详情:")
            print("帧号  | 动态范围均值 | 动态范围标准差 | ultra_low | low | medium_low | medium_high | high | ultra_high | 总通道")
            print("-" * 100)

            for i, frame_info in enumerate(data['frames']):
                frame_idx = frame_info.get('frame_index', i)
                mean_range = frame_info['mean_range']
                std_range = frame_info['std_range']

                # 获取各分组通道数
                groups = frame_info['groups']
                ultra_low = groups.get('ultra_low', {}).get('count', 0)
                low = groups.get('low', {}).get('count', 0)
                medium_low = groups.get('medium_low', {}).get('count', 0)
                medium_high = groups.get('medium_high', {}).get('count', 0)
                high = groups.get('high', {}).get('count', 0)
                ultra_high = groups.get('ultra_high', {}).get('count', 0)
                total = ultra_low + low + medium_low + medium_high + high + ultra_high

                print(f"{frame_idx:4d}  | {mean_range:11.4f} | {std_range:13.4f} | {ultra_low:9d} | {low:3d} | {medium_low:10d} | {medium_high:11d} | {high:4d} | {ultra_high:10d} | {total:6d}")

            # 显示每帧各分组的min/max值
            print("\n每帧各分组的最小值和最大值:")
            for i, frame_info in enumerate(data['frames']):
                frame_idx = frame_info.get('frame_index', i)
                print(f"\n帧 {frame_idx}:")
                groups = frame_info['groups']

                for group_name in ['ultra_low', 'low', 'medium_low', 'medium_high', 'high', 'ultra_high']:
                    if group_name in groups and groups[group_name].get('count', 0) > 0:
                        group_info = groups[group_name]
                        global_min = group_info.get('global_min', 0)
                        global_max = group_info.get('global_max', 0)
                        count = group_info.get('count', 0)
                        channels = group_info.get('channels', [])
                        print(f"  {group_name:12}: {count:3d} 个通道, 范围=[{global_min:8.4f}, {global_max:8.4f}], 通道索引={channels[:10]}{'...' if len(channels) > 10 else ''}")

            # 计算各分组的平均通道数
            group_totals = defaultdict(int)
            for frame_info in data['frames']:
                for group_name, group_info in frame_info['groups'].items():
                    group_totals[group_name] += group_info['count']

            print("\n各分组平均通道数:")
            total_channels = 0
            for group_name in ['ultra_low', 'low', 'medium_low', 'medium_high', 'high', 'ultra_high']:
                if group_name in group_totals:
                    avg_count = group_totals[group_name] / frame_count
                    total_channels += avg_count
                    print(f"  {group_name:12}: {avg_count:6.2f}")

            print(f"总通道数: {total_channels:.2f}")

    return group_data

def generate_summary_report(group_data, output_dir):
    """
    生成汇总报告
    """
    report_path = os.path.join(output_dir, "group_quantization_summary.txt")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("SFU数据集分组量化实验汇总报告\n")
        f.write("=" * 80 + "\n\n")
        
        f.write(f"总序列数: {len(set(seq for seq, qp in group_data.keys()))}\n")
        f.write(f"总配置数: {len(group_data)}\n")
        f.write(f"总帧数: {sum(len(data['frames']) for data in group_data.values())}\n\n")
        
        f.write("序列列表:\n")
        sequences = sorted(set(seq for seq, qp in group_data.keys()))
        for seq in sequences:
            qps = sorted([qp for s, qp in group_data.keys() if s == seq])
            f.write(f"  {seq}: QP {qps}\n")
        
        f.write("\n详细统计信息:\n")
        for (seq_name, qp), data in sorted(group_data.items()):
            f.write(f"\n序列: {seq_name}, QP: {qp}\n")
            f.write("=" * 80 + "\n")

            frame_count = len(data['frames'])
            f.write(f"总帧数: {frame_count}\n")

            if frame_count > 0:
                # 计算平均统计信息
                mean_ranges = [frame['mean_range'] for frame in data['frames']]
                std_ranges = [frame['std_range'] for frame in data['frames']]

                f.write(f"动态范围均值: {np.mean(mean_ranges):.4f} ± {np.std(mean_ranges):.4f}\n")
                f.write(f"动态范围标准差: {np.mean(std_ranges):.4f} ± {np.std(std_ranges):.4f}\n\n")

                # 写入每帧详细信息
                f.write("每帧分组详情:\n")
                f.write("帧号  | 动态范围均值 | 动态范围标准差 | ultra_low | low | medium_low | medium_high | high | ultra_high | 总通道\n")
                f.write("-" * 100 + "\n")

                for i, frame_info in enumerate(data['frames']):
                    frame_idx = frame_info.get('frame_index', i)
                    mean_range = frame_info['mean_range']
                    std_range = frame_info['std_range']

                    # 获取各分组通道数
                    groups = frame_info['groups']
                    ultra_low = groups.get('ultra_low', {}).get('count', 0)
                    low = groups.get('low', {}).get('count', 0)
                    medium_low = groups.get('medium_low', {}).get('count', 0)
                    medium_high = groups.get('medium_high', {}).get('count', 0)
                    high = groups.get('high', {}).get('count', 0)
                    ultra_high = groups.get('ultra_high', {}).get('count', 0)
                    total = ultra_low + low + medium_low + medium_high + high + ultra_high

                    f.write(f"{frame_idx:4d}  | {mean_range:11.4f} | {std_range:13.4f} | {ultra_low:9d} | {low:3d} | {medium_low:10d} | {medium_high:11d} | {high:4d} | {ultra_high:10d} | {total:6d}\n")

                # 写入每帧各分组的min/max值
                f.write("\n每帧各分组的最小值和最大值:\n")
                for i, frame_info in enumerate(data['frames']):
                    frame_idx = frame_info.get('frame_index', i)
                    f.write(f"\n帧 {frame_idx}:\n")
                    groups = frame_info['groups']

                    for group_name in ['ultra_low', 'low', 'medium_low', 'medium_high', 'high', 'ultra_high']:
                        if group_name in groups and groups[group_name].get('count', 0) > 0:
                            group_info = groups[group_name]
                            global_min = group_info.get('global_min', 0)
                            global_max = group_info.get('global_max', 0)
                            count = group_info.get('count', 0)
                            channels = group_info.get('channels', [])
                            f.write(f"  {group_name:12}: {count:3d} 个通道, 范围=[{global_min:8.4f}, {global_max:8.4f}], 通道索引={channels[:10]}{'...' if len(channels) > 10 else ''}\n")

                # 计算各分组的平均通道数
                group_totals = defaultdict(int)
                for frame_info in data['frames']:
                    for group_name, group_info in frame_info['groups'].items():
                        group_totals[group_name] += group_info['count']

                f.write("\n各分组平均通道数:\n")
                total_channels = 0
                for group_name in ['ultra_low', 'low', 'medium_low', 'medium_high', 'high', 'ultra_high']:
                    if group_name in group_totals:
                        avg_count = group_totals[group_name] / frame_count
                        total_channels += avg_count
                        f.write(f"  {group_name:12}: {avg_count:6.2f}\n")

                f.write(f"总通道数: {total_channels:.2f}\n\n")
    
    print(f"汇总报告保存在: {report_path}")

def select_sequence_and_qp(group_data):
    """
    交互式选择序列和QP
    """
    # 获取所有可用的序列和QP
    sequences = {}
    for (seq_name, qp), data in group_data.items():
        if seq_name not in sequences:
            sequences[seq_name] = []
        sequences[seq_name].append(qp)

    # 排序
    for seq_name in sequences:
        sequences[seq_name].sort()

    print("\n📋 可用的序列和QP:")
    seq_list = list(sequences.keys())
    for i, seq_name in enumerate(seq_list):
        qps = sequences[seq_name]
        print(f"  {i+1}. {seq_name}: QP {qps}")

    # 选择序列
    while True:
        try:
            seq_choice = input(f"\n请选择序列 (1-{len(seq_list)}, 或按回车选择全部): ").strip()
            if seq_choice == "":
                selected_sequences = seq_list
                break
            else:
                seq_idx = int(seq_choice) - 1
                if 0 <= seq_idx < len(seq_list):
                    selected_sequences = [seq_list[seq_idx]]
                    break
                else:
                    print("❌ 无效选择，请重新输入")
        except ValueError:
            print("❌ 请输入有效数字")

    # 选择QP
    if len(selected_sequences) == 1:
        seq_name = selected_sequences[0]
        available_qps = sequences[seq_name]
        print(f"\n📋 序列 {seq_name} 可用的QP: {available_qps}")

        while True:
            try:
                qp_choice = input(f"请选择QP ({available_qps}, 或按回车选择全部): ").strip()
                if qp_choice == "":
                    selected_qps = available_qps
                    break
                else:
                    qp = int(qp_choice)
                    if qp in available_qps:
                        selected_qps = [qp]
                        break
                    else:
                        print(f"❌ QP {qp} 不可用，请从 {available_qps} 中选择")
            except ValueError:
                print("❌ 请输入有效的QP数字")
    else:
        selected_qps = None  # 选择全部QP

    # 过滤数据
    filtered_data = {}
    for (seq_name, qp), data in group_data.items():
        if seq_name in selected_sequences:
            if selected_qps is None or qp in selected_qps:
                filtered_data[(seq_name, qp)] = data

    print(f"\n✅ 已选择 {len(filtered_data)} 个配置进行分析")
    return filtered_data

def main():
    """
    主函数
    """
    parser = argparse.ArgumentParser(description='SFU数据集分组量化结果分析工具')
    parser.add_argument('--sequence', type=str, help='指定序列名称，如：BasketballPass_416x240_50_val')
    parser.add_argument('--qp', type=int, help='指定QP值，如：24')
    parser.add_argument('--interactive', action='store_true', help='使用交互式选择模式')

    args = parser.parse_args()

    print("=" * 80)
    print("SFU数据集分组量化结果分析工具 - 简化版")
    print("=" * 80)

    # 配置路径 - 自动检测可能的路径
    possible_paths = [
        "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output_group_test/split-inference-video/fctmfcm7.0_ori_SFU/SFUHW/"
    ]

    base_dir = None
    for path in possible_paths:
        if os.path.exists(path):
            base_dir = path
            print(f"使用输出目录: {base_dir}")
            break

    if base_dir is None:
        print("❌ 找不到输出目录，尝试在当前目录搜索...")
        base_dir = "/work/Users/<USER>/fctm-v7.0"

    output_dir = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation"

    # 1. 加载数据
    print("步骤1: 加载分组信息文件...")
    group_data = load_group_info_files(base_dir)

    if not group_data:
        print("❌ 没有找到有效的分组信息文件!")
        return

    # 2. 选择要分析的序列和QP
    print("步骤2: 选择分析目标...")

    if args.sequence or args.qp:
        # 命令行参数模式
        selected_data = {}
        for (seq_name, qp), data in group_data.items():
            if args.sequence and seq_name != args.sequence:
                continue
            if args.qp and qp != args.qp:
                continue
            selected_data[(seq_name, qp)] = data

        if not selected_data:
            print(f"❌ 没有找到匹配的数据: sequence={args.sequence}, qp={args.qp}")
            return

        print(f"✅ 通过命令行参数选择了 {len(selected_data)} 个配置")

    elif args.interactive:
        # 交互式选择模式
        selected_data = select_sequence_and_qp(group_data)
    else:
        # 默认使用全部数据
        selected_data = group_data
        print(f"✅ 使用全部数据 ({len(selected_data)} 个配置)")

    # 3. 分析分组分布
    print("步骤3: 分析分组分布...")
    analyze_group_distribution(selected_data)

    # 4. 生成汇总报告
    print("步骤4: 生成汇总报告...")
    generate_summary_report(selected_data, output_dir)

    print("\n" + "=" * 80)
    print("✅ 分组量化结果分析完成!")
    print("=" * 80)

if __name__ == "__main__":
    main()
