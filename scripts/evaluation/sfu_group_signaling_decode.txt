bash ./sfu_hw_obj/fctm_eval_on_sfu_hw_obj.sh --testdata /work/Users/<USER>/fcm_testdata --inner_codec /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3 --output_dir /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output --exp_name group_SFU_verify --device cuda --qp 24 --seq_name BasketballPass_416x240_50_val --extra_params '++pipeline.codec.group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data ++pipeline.codec.decode_only=true'
b'============================== RUNNING FCTM + COMPRESSAI-VISION ==================================\n'
b'Datatset location:   /work/Users/<USER>/fcm_testdata\n'
b'Output directory:    /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output\n'
b'Experiment folder:   fctmgroup_SFU_verify\n'
b'Running Device:      cuda\n'
b'Input sequence:      BasketballPass_416x240_50_val\n'
b'Seq. Framerate:      50\n'
b'QP for Inner Codec:  24\n'
b'Intra Period for Inner Codec: 64\n'
b'Other Parameters:    ++pipeline.codec.group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data ++pipeline.codec.decode_only=true\n'
b'==================================================================================================\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'[2025-07-29 00:29:40,335][fvcore.common.checkpoint][INFO] - [Checkpointer] Loading from /work/Users/<USER>/fctm-v7.0/compressai_vision/compressai_vision/model_wrappers/../../weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl ...\n'
b"[2025-07-29 00:29:40,541][fvcore.common.checkpoint][INFO] - Reading a file from 'Detectron2 Model Zoo'\n"
b'[2025-07-29 00:29:40,676][detectron2.data.datasets.coco][INFO] - Loaded 97 images in COCO format from /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/annotations/BasketballPass_416x240_50_val.json\n'
b'[2025-07-29 00:29:40,679][Detectron2Dataset][INFO] - "sfu-hw-BasketballPass_416x240_50_val" successfully registred.\n'
b'[2025-07-29 00:29:40,679][detectron2.data.common][INFO] - Serializing 97 elements to byte tensors and concatenating them all ...\n'
b'[2025-07-29 00:29:40,680][detectron2.data.common][INFO] - Serialized dataset takes 0.06 MiB\n'
b"[2025-07-29 00:29:40,680][detectron2.data.dataset_mapper][INFO] - [DatasetMapper] Augmentations used in inference: [ResizeShortestEdge(short_edge_length=(800, 800), max_size=1333, sample_style='choice')]\n"
b"[2025-07-29 00:29:40,681][detectron2.data.dataset_mapper][INFO] - [DatasetMapper] Augmentations used in inference: [ResizeShortestEdge(short_edge_length=(800, 800), max_size=1333, sample_style='choice')]\n"
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'[2025-07-29 00:29:41,655][compressai_vision.run.eval_split_inference][INFO] -                 \n'
b' ============================================================                \n'
b' Pipeline                   : VideoSplitInference                           \n'
b' Vision Model               : faster_rcnn_X_101_32x8d_FPN_3x                \n'
b"  -- Split Point            : ['p2', 'p3', 'p4', 'p5']                \n"
b'  -- Cfg                    : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/models/detectron2/configs/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x.yaml                \n'
b'  -- Weights                : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl                \n'
b' Codec                      : FCTM                                          \n'
b'  -- Counted # CPUs for use : 64                \n'
b'  -- Enc. Only              : False                 \n'
b'  -- Dec. Only              : True                 \n'
b'  -- Output Dir             : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output                 \n'
b'  -- Skip N-Frames          : 0                 \n'
b'  -- # Frames To Be Coded   : -1                 \n'
b'  -- Bitstream              : sfu-hw-BasketballPass_416x240_50_val_qp24.bin                 \n'
b' Dataset                    : sfu-hw-BasketballPass_416x240_50_val                 \n'
b'  -- Data                   : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/images                 \n'
b'  -- Annotation             : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/annotations/BasketballPass_416x240_50_val.json                 \n'
b'  -- SEQ-INFO               : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/seqinfo.ini                         \n'
b' Evaluator                  : COCOEVal                                      \n'
b'  -- DataCatalog            : SFUHW                 \n'
b'  -- Output Dir             : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/evaluation                 \n'
b'  -- Output file            : COCOEVal_on_SFUHW_sfu-hw-BasketballPass_416x240_50_val         \n'
b'\n'
b'\n'
b'[INFO] Found group_quantization_path in config: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[YUV_INJECT] \xe4\xbd\xbf\xe7\x94\xa8\xe9\xa2\x84\xe8\xae\xbeYUV\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400_input.yuv\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 0\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 0 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2857, 2.5570]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.8652, 4.0280]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0042, 7.5744]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.4480, 14.5951]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 0 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0000\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 1\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 1 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2337, 2.2245]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.3516, 4.0890]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.6598, 6.4736]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.7179, 13.5553]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 1 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0001\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 2\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 2 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2710, 2.1747]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.7471, 4.3834]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.1986, 6.0095]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.1818, 13.2596]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 2 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0002\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 3\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 3 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2505, 2.1517]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.3150, 4.7642]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 4 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.3914, 5.9295]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.1725, 12.4803]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 3 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0003\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 4\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 4 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2041, 2.3434]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.7500, 4.2597]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4244, 7.5781]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.6772, 12.0961]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 4 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0004\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 5\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 5 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2243, 2.3427]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 48 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.7434, 3.9087]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 4 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.3112, 5.3546]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.1478, 14.2012]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 5 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0005\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 6\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 6 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1986, 2.6510]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.8524, 4.5910]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.1061, 5.4830]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.8730, 13.5518]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 6 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0006\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 7\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 7 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.8363, 2.5674]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2436, 4.9008]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.6530, 7.4532]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.5461, 14.3337]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 7 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0007\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 8\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 8 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0715, 2.2507]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6401, 4.3198]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5780, 7.4850]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.4101, 11.7564]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 8 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0008\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 9\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 9 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1637, 2.1588]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2860, 4.1906]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.7448, 7.1576]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.0337, 10.9622]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 9 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0009\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 10\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 10 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3127, 2.2345]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.8178, 4.1087]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4341, 6.3136]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.8655, 13.0948]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 10 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0010\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 11\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 11 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4168, 2.0451]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9215, 4.3862]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.7195, 7.5418]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.2923, 14.0737]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 11 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0011\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 12\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 12 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4183, 2.2963]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.4667, 4.3321]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.5008, 7.1561]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.9337, 11.1943]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 12 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0012\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 13\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 13 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3535, 2.1834]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9037, 4.2957]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.0771, 7.6337]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.6153, 11.3638]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 13 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0013\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 14\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 14 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2529, 2.3095]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.4765, 4.3188]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.1281, 5.7209]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.9634, 11.9805]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 14 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0014\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 15\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 15 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.9524, 2.2855]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9641, 5.0980]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.1410, 6.5358]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.6146, 11.6698]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 15 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0015\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 16\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 16 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.8070, 2.1829]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5402, 4.2366]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.9484, 6.1025]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.0214, 12.1371]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 16 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0016\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 17\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 17 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1321, 2.3197]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1363, 3.8797]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4246, 6.7958]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.4060, 12.3709]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 17 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0017\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 18\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 18 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2136, 2.0520]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.7266, 3.5929]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4209, 7.7520]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.2664, 13.7906]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 18 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0018\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 19\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 19 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.9760, 2.3924]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1928, 3.8141]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.3036, 6.7851]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.2382, 14.1923]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 19 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0019\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 20\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 20 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1147, 2.6689]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1978, 3.9225]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.0369, 6.1406]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.9388, 11.9721]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 20 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0020\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 21\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 21 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2235, 2.4410]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.5444, 4.0289]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.9662, 7.1875]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.1774, 10.7802]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 21 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0021\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 22\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 22 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1721, 1.9996]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6468, 3.8530]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.3482, 6.1362]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.4875, 11.3578]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 22 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0022\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 23\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 23 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 109 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3979, 2.3177]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1006, 4.0312]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.5459, 6.7011]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.7987, 12.8099]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 23 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0023\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 24\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 24 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1999, 2.0197]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9473, 4.3874]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.1060, 6.2389]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.6504, 14.7468]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 24 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0024\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 25\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 25 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3156, 2.5923]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.4447, 4.3373]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0105, 5.8138]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.3964, 14.0210]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 25 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0025\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 26\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 26 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 109 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3548, 2.5809]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 36 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0677, 3.9109]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0942, 5.0631]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.4603, 11.8809]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 26 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0026\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 27\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 27 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1795, 2.2300]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.8825, 4.2912]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.2193, 5.6506]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.8965, 12.2624]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 27 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0027\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 28\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 28 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 112 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2203, 2.3633]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 32 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2043, 4.8088]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.2929, 6.7959]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.1332, 15.0524]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 28 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0028\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 29\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 29 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1663, 2.3785]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.6970, 4.5623]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.8948, 6.7979]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.3002, 16.0880]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 29 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0029\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 30\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 30 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3096, 2.3030]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.8645, 4.8305]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.2528, 6.0820]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.1774, 14.1280]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 30 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0030\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 31\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 31 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0766, 2.1593]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2529, 4.5176]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4152, 6.0613]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.1423, 14.3999]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 31 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0031\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 32\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 32 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3962, 2.4513]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2602, 4.3109]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.8108, 6.4196]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.5361, 15.6337]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 32 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0032\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 33\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 33 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 97 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3003, 2.0809]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 51 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1880, 4.0231]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.7006, 7.1563]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.7294, 18.5623]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 33 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0033\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 34\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 34 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4793, 2.4558]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0908, 4.7902]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.7971, 6.0656]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.6667, 15.9185]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 34 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0034\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 35\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 35 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0320, 2.1944]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6032, 3.9966]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.4483, 6.1031]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.8523, 14.6254]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 35 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0035\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 36\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 36 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.7047, 2.6229]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5707, 4.7322]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.5403, 6.2506]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.0509, 14.7554]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 36 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0036\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 37\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 37 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1946, 2.7281]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.0721, 4.3000]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0471, 7.7585]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.0948, 14.0039]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 37 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0037\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 38\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 38 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0074, 2.4515]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.9362, 4.1271]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.1055, 6.8183]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.7667, 14.2929]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 38 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0038\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 39\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 39 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4592, 2.4166]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.4192, 4.1632]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.2797, 5.7137]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.1649, 15.2120]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 39 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0039\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 40\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 40 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3397, 2.4364]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5569, 4.2946]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.4111, 6.7469]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.4791, 15.6740]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 40 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0040\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 41\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 41 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1962, 2.3682]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1622, 5.0906]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.3577, 7.5191]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.4671, 15.5155]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 41 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0041\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 42\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 42 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2910, 2.2584]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.1506, 4.8074]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.9126, 7.3107]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.6247, 15.1203]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 42 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0042\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 43\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 43 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3655, 2.4646]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.7880, 4.2267]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.6222, 7.1819]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.7494, 14.1856]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 43 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0043\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 44\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 44 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.5761, 2.2639]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5158, 4.3649]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.7752, 7.0096]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.9595, 17.1847]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 44 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0044\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 45\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 45 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3755, 2.5491]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2808, 4.9735]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.3231, 7.9269]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.1416, 15.6035]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 45 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0045\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 46\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 46 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3022, 2.6489]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.9506, 5.1524]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.1038, 7.5277]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.0389, 13.9511]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 46 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0046\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 47\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 47 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4701, 2.6899]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1479, 4.6168]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.3768, 7.9147]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.9470, 16.4187]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 47 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0047\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 48\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 48 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4201, 2.5601]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.6397, 4.7632]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 13 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.4520, 6.6606]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.3865, 14.2889]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 48 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0048\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 49\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 49 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.7785, 2.5172]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0894, 5.6034]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 13 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.6862, 7.7055]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.8207, 16.6206]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 49 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0049\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 50\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 50 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4906, 2.8036]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9958, 4.7089]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.5391, 7.4210]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.2277, 14.1107]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 50 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0050\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 51\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 51 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2378, 2.6807]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.8461, 4.9218]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.7375, 6.8159]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.4214, 12.2485]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 51 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0051\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 52\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 52 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 98 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.6565, 2.6590]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.6124, 5.0719]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.6179, 8.3419]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.7982, 13.0814]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 52 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0052\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 53\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 53 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 98 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4117, 2.7451]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.0592, 5.0357]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.5388, 7.7200]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.6519, 13.7867]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 53 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0053\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 54\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 54 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 96 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2157, 2.5949]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 48 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5751, 5.1411]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.9440, 7.1598]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.1858, 14.5023]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 54 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0054\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 55\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 55 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 97 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.6532, 2.4695]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.6841, 4.8134]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 14 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0768, 7.0829]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.4289, 13.1079]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 55 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0055\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 56\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 56 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3585, 2.2518]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.4613, 4.3883]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.8619, 6.6935]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.5704, 13.9050]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 56 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0056\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 57\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 57 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2172, 2.3840]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6693, 5.1147]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.8432, 6.5552]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.8497, 14.2576]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 57 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0057\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 58\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 58 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4260, 2.5747]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0140, 5.1719]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0043, 7.7495]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.0319, 15.2292]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 58 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0058\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 59\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 59 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2537, 2.2589]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5597, 5.2975]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.4031, 6.0114]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-15.0414, 16.5320]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 59 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0059\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 60\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 60 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2625, 2.2054]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.4958, 4.7910]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.7434, 8.5420]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-15.3923, 18.2043]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 60 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0060\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 61\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 61 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0627, 2.4902]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.1536, 5.2103]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.2307, 7.7516]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.8665, 14.7383]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 61 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0061\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 62\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 62 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2554, 2.4467]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.7456, 4.5822]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.2220, 7.9505]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.2078, 14.8790]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 62 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0062\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 63\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 63 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1189, 2.0954]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6630, 5.0099]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0276, 7.5510]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.2639, 12.9243]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 63 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0063\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 64\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 64 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1031, 1.8943]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5154, 4.6943]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.9733, 6.7770]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.3225, 15.1908]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 64 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0064\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 65\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 65 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0187, 1.8612]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9713, 4.2613]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.2141, 7.1935]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.6777, 15.0747]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 65 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0065\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 66\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 66 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2741, 2.0239]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.7762, 3.7363]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0221, 7.0150]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.4148, 15.3909]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 66 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0066\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 67\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 67 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1429, 2.2138]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.3056, 4.1061]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4524, 7.2709]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.8048, 14.5233]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 67 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0067\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 68\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 68 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1754, 2.2414]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5595, 4.4969]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.1907, 7.3108]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.9847, 14.1183]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 68 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0068\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 69\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 69 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1893, 2.0747]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 49 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9268, 4.2335]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.8862, 7.6676]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.8017, 14.0293]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 69 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0069\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 70\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 70 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.3574, 2.1808]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.9951, 4.0527]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.1927, 7.6083]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.0455, 14.4188]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 70 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0070\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 71\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 71 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2353, 2.3884]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5865, 4.9610]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.8376, 6.9932]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.5264, 14.1854]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 71 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0071\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 72\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 72 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1778, 2.2565]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6934, 4.4457]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.4638, 6.3264]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.3120, 13.1547]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 72 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0072\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 73\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 73 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2983, 2.3447]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.8730, 3.7237]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.9647, 6.6938]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.2390, 13.0947]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 73 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0073\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 74\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 74 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2770, 2.3225]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0536, 4.3948]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.5990, 6.4713]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.5435, 12.0004]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 74 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0074\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 75\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 75 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0367, 2.2095]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6971, 3.8417]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.8188, 6.2547]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.4790, 13.3526]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 75 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0075\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 76\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 76 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1894, 1.9976]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.3665, 5.3512]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.3825, 7.3426]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.8195, 14.3910]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 76 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0076\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 77\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 77 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2317, 1.9953]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2765, 4.3493]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4469, 6.7796]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.9266, 13.9174]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 77 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0077\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 78\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 78 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0152, 2.0426]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.5433, 4.1095]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.8720, 6.6027]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.5358, 14.3896]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 78 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0078\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 79\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 79 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2016, 2.3031]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4646, 4.4128]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.2173, 6.5890]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.2909, 13.6248]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 79 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0079\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 80\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 80 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0159, 2.2356]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.3387, 4.7395]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.3608, 6.4136]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.7909, 13.3625]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 80 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0080\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 81\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 81 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1418, 1.9334]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.8821, 4.6355]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.0791, 6.9063]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.0562, 15.3097]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 81 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0081\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 82\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 82 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.9171, 2.4965]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.1685, 3.9002]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.1483, 6.5210]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.4389, 14.8833]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 82 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0082\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 83\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 83 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.1538, 2.1927]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.8256, 4.6248]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.1514, 7.1983]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.0493, 14.3619]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 83 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0083\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 84\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 84 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0329, 2.1663]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.2389, 4.6235]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4985, 7.3119]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.5156, 12.5161]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 84 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0084\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 85\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 85 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4961, 2.4097]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.6812, 4.5591]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.0734, 6.5815]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.5784, 13.6692]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 85 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0085\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 86\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 86 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0710, 2.1097]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.2650, 4.5780]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.9474, 6.5232]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.3117, 10.9259]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 86 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0086\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 87\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 87 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.9370, 2.0836]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.7690, 4.9865]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.5341, 5.6207]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.1757, 12.7917]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 87 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0087\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 88\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 88 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.4102, 2.4618]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6672, 4.9870]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.9383, 5.4197]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.2393, 12.9197]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 88 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0088\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 89\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 89 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.9919, 1.9745]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.5842, 5.0745]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.6068, 5.3181]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.7297, 11.6072]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 89 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0089\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 90\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 90 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0336, 1.8081]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.6100, 3.8916]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.2693, 5.0508]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.3251, 13.2255]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 90 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0090\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 91\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 91 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.8802, 2.3326]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.5411, 4.4363]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.9769, 6.0783]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.3068, 11.4538]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 91 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0091\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 92\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 92 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.9847, 2.0022]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.8532, 4.4026]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.4447, 6.6807]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.4075, 10.3468]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 92 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0092\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 93\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 93 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.8814, 2.2402]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0038, 3.6224]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.7728, 6.0450]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.1331, 11.4126]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 93 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0093\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 94\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 94 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.0293, 2.4711]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0093, 4.1387]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.3651, 6.6588]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.9868, 11.6912]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 94 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0094\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 95\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 95 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.2821, 2.4204]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.0452, 4.2823]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.2583, 6.1928]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.3818, 13.0273]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 95 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0095\n'
b'\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xef\xbc\x81\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\x96\x87\xe4\xbb\xb6: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe5\xbd\x93\xe5\x89\x8d\xe5\xb8\xa7\xe7\xb4\xa2\xe5\xbc\x95: 96\n'
b'[DEBUG] \xe6\x89\xbe\xe5\x88\xb0\xe5\xb8\xa7 96 \xe7\x9a\x84\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.9802, 2.6547]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.3497, 4.1502]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.1322, 7.4973]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.7066, 14.5062]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe9\x80\x86\xe5\xbd\x92\xe4\xb8\x80\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe8\xa7\xa3\xe7\xa0\x81\xe7\xab\xaf\xe5\xb8\xa7 96 \xe7\x9a\x84\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0096\n'
b'[2025-07-29 00:29:55,401][VideoSplitInference][INFO] - Processing NN-Part2...\n'
b'\r  0%|          | 0/97 [00:00<?, ?it/s]/work/Users/<USER>/anaconda/envs/fctm7.0/lib/python3.8/site-packages/torch/functional.py:504: UserWarning: torch.meshgrid: in an upcoming release, it will be required to pass the indexing argument. (Triggered internally at ../aten/src/ATen/native/TensorShape.cpp:3483.)\n'
b'  return _VF.meshgrid(tensors, **kwargs)  # type: ignore[attr-defined]\n'
b'\r  1%|          | 1/97 [00:00<00:22,  4.25it/s]\r  3%|\xe2\x96\x8e         | 3/97 [00:00<00:09,  9.79it/s]\r  5%|\xe2\x96\x8c         | 5/97 [00:00<00:07, 12.88it/s]\r  7%|\xe2\x96\x8b         | 7/97 [00:00<00:06, 14.78it/s]\r  9%|\xe2\x96\x89         | 9/97 [00:00<00:05, 16.01it/s]\r 11%|\xe2\x96\x88\xe2\x96\x8f        | 11/97 [00:00<00:05, 16.61it/s]\r 13%|\xe2\x96\x88\xe2\x96\x8e        | 13/97 [00:00<00:04, 17.20it/s]\r 15%|\xe2\x96\x88\xe2\x96\x8c        | 15/97 [00:00<00:04, 17.60it/s]\r 18%|\xe2\x96\x88\xe2\x96\x8a        | 17/97 [00:01<00:04, 17.87it/s]\r 20%|\xe2\x96\x88\xe2\x96\x89        | 19/97 [00:01<00:04, 18.05it/s]\r 22%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f       | 21/97 [00:01<00:04, 18.19it/s]\r 24%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e       | 23/97 [00:01<00:04, 18.29it/s]\r 26%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c       | 25/97 [00:01<00:03, 18.29it/s]\r 28%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a       | 27/97 [00:01<00:03, 18.33it/s]\r 30%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89       | 29/97 [00:01<00:03, 18.41it/s]\r 32%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f      | 31/97 [00:01<00:03, 18.43it/s]\r 34%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d      | 33/97 [00:01<00:03, 18.47it/s]\r 36%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c      | 35/97 [00:02<00:03, 18.48it/s]\r 38%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a      | 37/97 [00:02<00:03, 18.51it/s]\r 40%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88      | 39/97 [00:02<00:03, 18.54it/s]\r 43%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e     | 42/97 [00:02<00:02, 19.98it/s]\r 46%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b     | 45/97 [00:02<00:02, 21.94it/s]\r 49%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89     | 48/97 [00:02<00:02, 23.36it/s]\r 53%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e    | 51/97 [00:02<00:01, 24.47it/s]\r 56%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c    | 54/97 [00:02<00:01, 25.29it/s]\r 59%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89    | 57/97 [00:02<00:01, 25.88it/s]\r 62%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f   | 60/97 [00:03<00:01, 26.33it/s]\r 65%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d   | 63/97 [00:03<00:01, 26.38it/s]\r 68%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a   | 66/97 [00:03<00:01, 26.69it/s]\r 71%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88   | 69/97 [00:03<00:01, 26.93it/s]\r 74%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d  | 72/97 [00:03<00:00, 27.03it/s]\r 77%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b  | 75/97 [00:03<00:00, 27.12it/s]\r 80%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88  | 78/97 [00:03<00:00, 27.19it/s]\r 84%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e | 81/97 [00:03<00:00, 27.23it/s]\r 87%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b | 84/97 [00:03<00:00, 27.28it/s]\r 90%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89 | 87/97 [00:04<00:00, 27.05it/s]\r 93%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e| 90/97 [00:04<00:00, 27.14it/s]\r 96%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c| 93/97 [00:04<00:00, 27.15it/s]\r 99%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89| 96/97 [00:04<00:00, 27.19it/s]\r100%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88| 97/97 [00:04<00:00, 20.58it/s][2025-07-29 00:30:00,127][detectron2.evaluation.coco_evaluation][INFO] - Preparing results for COCO format ...\n'
b'[2025-07-29 00:30:00,128][detectron2.evaluation.coco_evaluation][INFO] - Saving results to /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/evaluation/coco_instances_results.json\n'
b'[2025-07-29 00:30:00,135][detectron2.evaluation.coco_evaluation][INFO] - Evaluating predictions with official COCO API...\n'
b'Loading and preparing results...\n'
b'DONE (t=0.00s)\n'
b'creating index...\n'
b'index created!\n'
b'Running per image evaluation...\n'
b'Evaluate annotation type *bbox*\n'
b'DONE (t=0.33s).\n'
b'Accumulating evaluation results...\n'
b'DONE (t=0.06s).\n'
b' Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.371\n'
b' Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.605\n'
b' Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.382\n'
b' Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.125\n'
b' Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.373\n'
b' Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.649\n'
b' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.208\n'
b' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.460\n'
b' Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.461\n'
b' Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.268\n'
b' Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.467\n'
b' Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.703\n'
b'[2025-07-29 00:30:00,531][detectron2.evaluation.coco_evaluation][INFO] - Evaluation results for bbox: \n'
b'|   AP   |  AP50  |  AP75  |  APs   |  APm   |  APl   |\n'
b'|:------:|:------:|:------:|:------:|:------:|:------:|\n'
b'| 37.085 | 60.476 | 38.168 | 12.507 | 37.335 | 64.936 |\n'
b'[2025-07-29 00:30:00,533][detectron2.evaluation.coco_evaluation][INFO] - Per-category bbox AP: \n'
b'| category      | AP     | category     | AP   | category       | AP     |\n'
b'|:--------------|:-------|:-------------|:-----|:---------------|:-------|\n'
b'| person        | 60.656 | bicycle      | nan  | car            | nan    |\n'
b'| motorcycle    | nan    | airplane     | nan  | bus            | nan    |\n'
b'| train         | nan    | truck        | nan  | boat           | nan    |\n'
b'| traffic light | nan    | fire hydrant | nan  | stop sign      | nan    |\n'
b'| parking meter | nan    | bench        | nan  | bird           | nan    |\n'
b'| cat           | nan    | dog          | nan  | horse          | nan    |\n'
b'| sheep         | nan    | cow          | nan  | elephant       | nan    |\n'
b'| bear          | nan    | zebra        | nan  | giraffe        | nan    |\n'
b'| backpack      | nan    | umbrella     | nan  | handbag        | nan    |\n'
b'| tie           | nan    | suitcase     | nan  | frisbee        | nan    |\n'
b'| skis          | nan    | snowboard    | nan  | sports ball    | 16.991 |\n'
b'| kite          | nan    | baseball bat | nan  | baseball glove | nan    |\n'
b'| skateboard    | nan    | surfboard    | nan  | tennis racket  | nan    |\n'
b'| bottle        | nan    | wine glass   | nan  | cup            | nan    |\n'
b'| fork          | nan    | knife        | nan  | spoon          | nan    |\n'
b'| bowl          | nan    | banana       | nan  | apple          | nan    |\n'
b'| sandwich      | nan    | orange       | nan  | broccoli       | nan    |\n'
b'| carrot        | nan    | hot dog      | nan  | pizza          | nan    |\n'
b'| donut         | nan    | cake         | nan  | chair          | 33.607 |\n'
b'| couch         | nan    | potted plant | nan  | bed            | nan    |\n'
b'| dining table  | nan    | toilet       | nan  | tv             | nan    |\n'
b'| laptop        | nan    | mouse        | nan  | remote         | nan    |\n'
b'| keyboard      | nan    | cell phone   | nan  | microwave      | nan    |\n'
b'| oven          | nan    | toaster      | nan  | sink           | nan    |\n'
b'| refrigerator  | nan    | book         | nan  | clock          | nan    |\n'
b'| vase          | nan    | scissors     | nan  | teddy bear     | nan    |\n'
b'| hair drier    | nan    | toothbrush   | nan  |                |        |\n'
b'====================================================================================================\n'
b'Encoding Information [Top 5 Rows...][VideoSplitInference()]\n'
b'\xe2\x95\x92\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa4\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x95\n'
b'\xe2\x94\x82    \xe2\x94\x82  input_datatype  \xe2\x94\x82  input_size  \xe2\x94\x82               file_name               \xe2\x94\x82   qp \xe2\x94\x82   bytes \xe2\x94\x82   coded_order \xe2\x94\x82  org_input_size  \xe2\x94\x82   total_pixels \xe2\x94\x82\n'
b'\xe2\x95\x9e\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xaa\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa1\n'
b'\xe2\x94\x82  0 \xe2\x94\x82  torch.float32   \xe2\x94\x82 (769, 1333)  \xe2\x94\x82 BasketballPass_416x240_50_val_000.png \xe2\x94\x82   24 \xe2\x94\x82 4991.81 \xe2\x94\x82             0 \xe2\x94\x82     240x416      \xe2\x94\x82          99840 \xe2\x94\x82\n'
b'\xe2\x94\x9c\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xa4\n'
b'\xe2\x94\x82  1 \xe2\x94\x82  torch.float32   \xe2\x94\x82 (769, 1333)  \xe2\x94\x82 BasketballPass_416x240_50_val_001.png \xe2\x94\x82   24 \xe2\x94\x82 4991.81 \xe2\x94\x82             1 \xe2\x94\x82     240x416      \xe2\x94\x82          99840 \xe2\x94\x82\n'
b'\xe2\x94\x9c\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xa4\n'
b'\xe2\x94\x82  2 \xe2\x94\x82  torch.float32   \xe2\x94\x82 (769, 1333)  \xe2\x94\x82 BasketballPass_416x240_50_val_002.png \xe2\x94\x82   24 \xe2\x94\x82 4991.81 \xe2\x94\x82             2 \xe2\x94\x82     240x416      \xe2\x94\x82          99840 \xe2\x94\x82\n'
b'\xe2\x94\x9c\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xa4\n'
b'\xe2\x94\x82  3 \xe2\x94\x82  torch.float32   \xe2\x94\x82 (769, 1333)  \xe2\x94\x82 BasketballPass_416x240_50_val_003.png \xe2\x94\x82   24 \xe2\x94\x82 4991.81 \xe2\x94\x82             3 \xe2\x94\x82     240x416      \xe2\x94\x82          99840 \xe2\x94\x82\n'
b'\xe2\x94\x9c\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xbc\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\x80\xe2\x94\xa4\n'
b'\xe2\x94\x82  4 \xe2\x94\x82  torch.float32   \xe2\x94\x82 (769, 1333)  \xe2\x94\x82 BasketballPass_416x240_50_val_004.png \xe2\x94\x82   24 \xe2\x94\x82 4991.81 \xe2\x94\x82             4 \xe2\x94\x82     240x416      \xe2\x94\x82          99840 \xe2\x94\x82\n'
b'\xe2\x95\x98\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\xa7\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x90\xe2\x95\x9b\n'
b'\n'
b'Summary files saved in : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/evaluation\n'
b'\n'
b'\n'
b'Performance Metrics Using Evaluation Criteria AP\n'
b'\n'
b'Frame Rate: 50, Total Frame: 97\n'
b'+----+---------------------------+-------+----------------------+------+------------------+----------------+-------------+----------+----------+----------------------+---------------------+------------------------------+-------------+\n'
b'|    | Dataset                   |   fps |   num_of_coded_frame |   qp |   bitrate (kbps) |   end_accuracy |   nn_part_1 |   encode |   decode |   decode_inner_codec |   decode_conversion |   decode_feature_restoration |   nn_part_2 |\n'
b'|----+---------------------------+-------+----------------------+------+------------------+----------------+-------------+----------+----------+----------------------+---------------------+------------------------------+-------------|\n'
b'|  0 | BasketballPass_416x240_50 |    50 |                   97 |   24 |          1996.73 |        37.0849 |           0 |        0 |  12.9027 |                0.001 |            0.860014 |                      11.9941 |     1.89676 |\n'
b'+----+---------------------------+-------+----------------------+------+------------------+----------------+-------------+----------+----------+----------------------+---------------------+------------------------------+-------------+\n'
b'\n'
b'\xe4\xbb\xbb\xe5\x8a\xa1\xe5\xae\x8c\xe6\x88\x90\n'



Traceback (most recent call last):
  File "../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py", line 403, in <module>
    output_df = generate_csv_classwise_video_map(
  File "../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py", line 156, in generate_csv_classwise_video_map
    results_df = read_df_rec(result_path, seq_list, nb_operation_points)
  File "../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py", line 66, in read_df_rec
    assert (
AssertionError: Did not find 4 results for sfu-hw-BasketballPass_416x240_50_val
python3 ../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py -r /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW -dp /work/Users/<USER>/fcm_testdata/SFU_HW_Obj -dn SFU
***************
