================================================================================
FCTM编码端和解码端数据对比分析
================================================================================
生成时间: 2025-07-28 17:09:49
编码端文件夹: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0000
解码端文件夹: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_decode_data/frame_0000

🔄 完整数据流转分析:
  数据流转路径:
    编码: 原始 → 归一化 → 量化 → 打包
    解码: 打包 → 逆量化 → 逆打包 → 重建

🔄 打包帧对比分析:
  编码端形状: (464, 96)
  解码端形状: (464, 96)
  数值差异:
    最大差异: 59.00000000
    平均差异: 7.61453867
    差异标准差: 5.93591118
  ⚠️  打包帧存在差异
    最大差异位置: (404, 73)
    编码端值: 442.000000
    解码端值: 501.000000

🔄 量化/逆量化过程对比:
  编码端量化张量形状: (164, 16, 24)
  解码端逆量化张量形状: (464, 96)
  ⚠️  形状不匹配，无法直接对比量化/逆量化过程
  说明: 编码端是3D张量(C,H,W)，解码端是2D打包帧(H,W)

🔄 重建质量对比:
  原始张量形状: (164, 16, 24)
  重建张量形状: (1, 164, 16, 24)
  调整后重建张量形状: (164, 16, 24)
  重建误差分析:
    最大重建误差: 1.143529
    平均重建误差: 0.069691
    误差标准差: 0.073013
    重建PSNR: 43.20 dB
  前10个通道的重建误差:
    通道 0: 平均误差=0.069359, 最大误差=0.251340
    通道 1: 平均误差=0.060085, 最大误差=0.256894
    通道 2: 平均误差=0.054001, 最大误差=0.330456
    通道 3: 平均误差=0.061076, 最大误差=0.322034
    通道 4: 平均误差=0.079789, 最大误差=0.371974
    通道 5: 平均误差=0.079360, 最大误差=0.398939
    通道 6: 平均误差=0.206503, 最大误差=0.799565
    通道 7: 平均误差=0.065776, 最大误差=0.312787
    通道 8: 平均误差=0.063208, 最大误差=0.382919
    通道 9: 平均误差=0.070401, 最大误差=0.274953

================================================================================
✅ 编码端和解码端对比分析完成!
================================================================================
