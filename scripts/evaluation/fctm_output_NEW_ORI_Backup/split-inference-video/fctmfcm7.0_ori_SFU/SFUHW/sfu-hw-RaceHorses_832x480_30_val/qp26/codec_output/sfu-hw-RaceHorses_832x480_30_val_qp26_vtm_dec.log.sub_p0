
VVCSoftware: VTM Decoder Version 23.3 [Linux][GCC 9.3.0][64 bit] [SIMD=AVX2] 
POC    0 LId:  0 TId: 0 ( IDR_N_LP, I-SLICE, QP 25 ) [DT  0.004] [L0] [L1] [:,(unk)] 
POC    1 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 0] [L1 0c] [:,(unk)] 
POC    2 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 1 0] [L1 1c 0] [:,(unk)] 
POC    3 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 2 1 0] [L1 2c 1 0] [:,(unk)] 
POC    4 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 3 2 1 0] [L1 3c 2 1 0] [:,(unk)] 
POC    5 LId:  0 TId: 0 ( TRAIL, B-<PERSON>IC<PERSON>, QP 33 ) [DT  0.001] [L0 4 3 2 0] [L1 4c 3 2 0] [:,(unk)] 
POC    6 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 5 4 3 0] [L1 5c 4 3 0] [:,(unk)] 
POC    7 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 6 5 4 0] [L1 6c 5 4 0] [:,(unk)] 
POC    8 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.002] [L0 7 6 5 0] [L1 7c 6 5 4] [:,(unk)] 
POC    9 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 8 7 6 0] [L1 8c 7 6 4] [:,(unk)] 
POC   10 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 9 8 7 0] [L1 9c 8 7 6] [:,(unk)] 
POC   11 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 10 9 8 0] [L1 10c 9 8 6] [:,(unk)] 
POC   12 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 11 10 8 0] [L1 11c 10 9 8] [:,(unk)] 
POC   13 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 12 11 8 0] [L1 12c 11 10 8] [:,(unk)] 
POC   14 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 13 12 8 0] [L1 13c 12 11 8] [:,(unk)] 
POC   15 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 14 13 8 0] [L1 14c 13 12 8] [:,(unk)] 
POC   16 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.002] [L0 15 14 8 0] [L1 15c 14 13 12] [:,(unk)] 
POC   17 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 16 15 8 0] [L1 16c 15 14 12] [:,(unk)] 
POC   18 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 17 16 8 0] [L1 17c 16 14 8] [:,(unk)] 
POC   19 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 18 16 8 0] [L1 18c 16 14 8] [:,(unk)] 
POC   20 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 19 16 8 0] [L1 19c 18 16 8] [:,(unk)] 
POC   21 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 20 16 8 0] [L1 20c 18 16 8] [:,(unk)] 
POC   22 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 21 16 8 0] [L1 21c 20 16 8] [:,(unk)] 
POC   23 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 22 16 8 0] [L1 22c 20 16 8] [:,(unk)] 
POC   24 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.002] [L0 23 16 8 0] [L1 23c 22 20 16] [:,(unk)] 
POC   25 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 24 16 8 0] [L1 24c 22 20 16] [:,(unk)] 
POC   26 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 25 24 16 8] [L1 25c 24 22 0] [:,(unk)] 
POC   27 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 26 24 16 8] [L1 26c 24 22 0] [:,(unk)] 
POC   28 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 27 24 16 8] [L1 27c 26 24 0] [:,(unk)] 
POC   29 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.000] [L0 28 24 16 8] [L1 28c 26 24 0] [:,(unk)] 
POC   30 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 29 24 16 8] [L1 29c 28 24 0] [:,(unk)] 
POC   31 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 30 24 16 8] [L1 30c 28 24 0] [:,(unk)] 
POC   32 LId:  0 TId: 0 ( CRA, I-SLICE, QP 25 ) [DT  0.003] [L0] [L1] [:,(unk)] 
POC   33 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.000] [L0 32] [L1 32c] [:,(unk)] 
POC   34 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 33 32] [L1 33c 32] [:,(unk)] 
POC   35 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 34 33 32] [L1 34c 33 32] [:,(unk)] 
POC   36 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 35 34 33 32] [L1 35c 34 33 32] [:,(unk)] 
POC   37 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.000] [L0 36 35 34 32] [L1 36c 35 34 32] [:,(unk)] 
POC   38 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 37 36 35 32] [L1 37c 36 35 32] [:,(unk)] 
POC   39 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 38 37 36 32] [L1 38c 37 36 32] [:,(unk)] 
POC   40 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.002] [L0 39 38 37 32] [L1 39c 38 37 36] [:,(unk)] 
POC   41 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 40 39 38 32] [L1 40c 39 38 36] [:,(unk)] 
POC   42 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 41 40 39 32] [L1 41c 40 39 38] [:,(unk)] 
POC   43 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 42 41 40 32] [L1 42c 41 40 38] [:,(unk)] 
POC   44 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 43 42 40 32] [L1 43c 42 41 40] [:,(unk)] 
POC   45 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 44 43 40 32] [L1 44c 43 42 40] [:,(unk)] 
POC   46 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 45 44 40 32] [L1 45c 44 43 40] [:,(unk)] 
POC   47 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 46 45 40 32] [L1 46c 45 44 40] [:,(unk)] 
POC   48 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.002] [L0 47 46 40 32] [L1 47c 46 45 44] [:,(unk)] 
POC   49 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 48 47 40 32] [L1 48c 47 46 44] [:,(unk)] 
POC   50 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 49 48 40 32] [L1 49c 48 46 40] [:,(unk)] 
POC   51 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 50 48 40 32] [L1 50c 48 46 40] [:,(unk)] 
POC   52 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 51 48 40 32] [L1 51c 50 48 40] [:,(unk)] 
POC   53 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 52 48 40 32] [L1 52c 50 48 40] [:,(unk)] 
POC   54 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 53 48 40 32] [L1 53c 52 48 40] [:,(unk)] 
POC   55 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 54 48 40 32] [L1 54c 52 48 40] [:,(unk)] 
POC   56 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.002] [L0 55 48 40 32] [L1 55c 54 52 48] [:,(unk)] 
POC   57 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 56 48 40 32] [L1 56c 54 52 48] [:,(unk)] 
POC   58 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 57 56 48 40] [L1 57c 56 54 32] [:,(unk)] 
POC   59 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 58 56 48 40] [L1 58c 56 54 32] [:,(unk)] 
POC   60 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 59 56 48 40] [L1 59c 58 56 32] [:,(unk)] 
POC   61 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 60 56 48 40] [L1 60c 58 56 32] [:,(unk)] 
POC   62 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 31 ) [DT  0.001] [L0 61 56 48 40] [L1 61c 60 56 32] [:,(unk)] 
POC   63 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 33 ) [DT  0.001] [L0 62 56 48 40] [L1 62c 60 56 32] [:,(unk)] 
POC   64 LId:  0 TId: 0 ( CRA, I-SLICE, QP 25 ) [DT  0.003] [L0] [L1] [:,(unk)] 

 Total Time:        0.156 sec.
