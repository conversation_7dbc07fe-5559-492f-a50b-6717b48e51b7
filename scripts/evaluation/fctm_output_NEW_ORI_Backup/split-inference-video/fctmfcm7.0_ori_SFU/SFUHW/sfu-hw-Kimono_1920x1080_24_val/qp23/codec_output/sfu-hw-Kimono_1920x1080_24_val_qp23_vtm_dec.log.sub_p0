
VVCSoftware: VTM Decoder Version 23.3 [Linux][GCC 9.3.0][64 bit] [SIMD=AVX2] 
POC    0 LId:  0 TId: 0 ( IDR_N_LP, I-SLICE, QP 22 ) [DT  0.005] [L0] [L1] [:,(unk)] 
POC    1 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.001] [L0 0] [L1 0c] [:,(unk)] 
POC    2 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 1 0] [L1 1c 0] [:,(unk)] 
POC    3 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.001] [L0 2 1 0] [L1 2c 1 0] [:,(unk)] 
POC    4 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 3 2 1 0] [L1 3c 2 1 0] [:,(unk)] 
POC    5 LId:  0 TId: 0 ( TRAIL, B-SLIC<PERSON>, QP 30 ) [DT  0.001] [L0 4 3 2 0] [L1 4c 3 2 0] [:,(unk)] 
POC    6 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 5 4 3 0] [L1 5c 4 3 0] [:,(unk)] 
POC    7 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.001] [L0 6 5 4 0] [L1 6c 5 4 0] [:,(unk)] 
POC    8 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 24 ) [DT  0.001] [L0 7 6 5 0] [L1 7c 6 5 4] [:,(unk)] 
POC    9 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 8 7 6 0] [L1 8c 7 6 4] [:,(unk)] 
POC   10 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.000] [L0 9 8 7 0] [L1 9c 8 7 6] [:,(unk)] 
POC   11 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 10 9 8 0] [L1 10c 9 8 6] [:,(unk)] 
POC   12 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 11 10 8 0] [L1 11c 10 9 8] [:,(unk)] 
POC   13 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 12 11 8 0] [L1 12c 11 10 8] [:,(unk)] 
POC   14 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 13 12 8 0] [L1 13c 12 11 8] [:,(unk)] 
POC   15 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 14 13 8 0] [L1 14c 13 12 8] [:,(unk)] 
POC   16 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 24 ) [DT  0.002] [L0 15 14 8 0] [L1 15c 14 13 12] [:,(unk)] 
POC   17 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 16 15 8 0] [L1 16c 15 14 12] [:,(unk)] 
POC   18 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.000] [L0 17 16 8 0] [L1 17c 16 14 8] [:,(unk)] 
POC   19 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 18 16 8 0] [L1 18c 16 14 8] [:,(unk)] 
POC   20 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 19 16 8 0] [L1 19c 18 16 8] [:,(unk)] 
POC   21 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 20 16 8 0] [L1 20c 18 16 8] [:,(unk)] 
POC   22 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 21 16 8 0] [L1 21c 20 16 8] [:,(unk)] 
POC   23 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.001] [L0 22 16 8 0] [L1 22c 20 16 8] [:,(unk)] 
POC   24 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 24 ) [DT  0.002] [L0 23 16 8 0] [L1 23c 22 20 16] [:,(unk)] 
POC   25 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 24 16 8 0] [L1 24c 22 20 16] [:,(unk)] 
POC   26 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.000] [L0 25 24 16 8] [L1 25c 24 22 0] [:,(unk)] 
POC   27 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 26 24 16 8] [L1 26c 24 22 0] [:,(unk)] 
POC   28 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 27 24 16 8] [L1 27c 26 24 0] [:,(unk)] 
POC   29 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 28 24 16 8] [L1 28c 26 24 0] [:,(unk)] 
POC   30 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 27 ) [DT  0.001] [L0 29 24 16 8] [L1 29c 28 24 0] [:,(unk)] 
POC   31 LId:  0 TId: 0 ( TRAIL, B-SLICE, QP 30 ) [DT  0.000] [L0 30 24 16 8] [L1 30c 28 24 0] [:,(unk)] 
POC   32 LId:  0 TId: 0 ( CRA, I-SLICE, QP 22 ) [DT  0.004] [L0] [L1] [:,(unk)] 

 Total Time:        0.103 sec.
