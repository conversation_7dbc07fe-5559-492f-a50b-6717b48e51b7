bash ./sfu_hw_obj/fctm_eval_on_sfu_hw_obj.sh --testdata /work/Users/<USER>/fcm_testdata --inner_codec /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3 --output_dir /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output --exp_name group_SFU_verify --device cuda --qp 24 --seq_name BasketballPass_416x240_50_val --extra_params '++pipeline.codec.group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data ++pipeline.codec.encode_only=true'
b'============================== RUNNING FCTM + COMPRESSAI-VISION ==================================\n'
b'Datatset location:   /work/Users/<USER>/fcm_testdata\n'
b'Output directory:    /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output\n'
b'Experiment folder:   fctmgroup_SFU_verify\n'
b'Running Device:      cuda\n'
b'Input sequence:      BasketballPass_416x240_50_val\n'
b'Seq. Framerate:      50\n'
b'QP for Inner Codec:  24\n'
b'Intra Period for Inner Codec: 64\n'
b'Other Parameters:    ++pipeline.codec.group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data ++pipeline.codec.encode_only=true\n'
b'==================================================================================================\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'[2025-07-28 23:12:32,666][fvcore.common.checkpoint][INFO] - [Checkpointer] Loading from /work/Users/<USER>/fctm-v7.0/compressai_vision/compressai_vision/model_wrappers/../../weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl ...\n'
b"[2025-07-28 23:12:32,890][fvcore.common.checkpoint][INFO] - Reading a file from 'Detectron2 Model Zoo'\n"
b'[2025-07-28 23:12:33,031][detectron2.data.datasets.coco][INFO] - Loaded 97 images in COCO format from /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/annotations/BasketballPass_416x240_50_val.json\n'
b'[2025-07-28 23:12:33,034][Detectron2Dataset][INFO] - "sfu-hw-BasketballPass_416x240_50_val" successfully registred.\n'
b'[2025-07-28 23:12:33,034][detectron2.data.common][INFO] - Serializing 97 elements to byte tensors and concatenating them all ...\n'
b'[2025-07-28 23:12:33,035][detectron2.data.common][INFO] - Serialized dataset takes 0.06 MiB\n'
b"[2025-07-28 23:12:33,035][detectron2.data.dataset_mapper][INFO] - [DatasetMapper] Augmentations used in inference: [ResizeShortestEdge(short_edge_length=(800, 800), max_size=1333, sample_style='choice')]\n"
b"[2025-07-28 23:12:33,036][detectron2.data.dataset_mapper][INFO] - [DatasetMapper] Augmentations used in inference: [ResizeShortestEdge(short_edge_length=(800, 800), max_size=1333, sample_style='choice')]\n"
b'[2025-07-28 23:12:33,041][COCOEVal][INFO] - creating output folder: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/evaluation\n'
b'fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision\n'
b'[2025-07-28 23:12:33,952][compressai_vision.run.eval_split_inference][INFO] -                 \n'
b' ============================================================                \n'
b' Pipeline                   : VideoSplitInference                           \n'
b' Vision Model               : faster_rcnn_X_101_32x8d_FPN_3x                \n'
b"  -- Split Point            : ['p2', 'p3', 'p4', 'p5']                \n"
b'  -- Cfg                    : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/models/detectron2/configs/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x.yaml                \n'
b'  -- Weights                : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl                \n'
b' Codec                      : FCTM                                          \n'
b'  -- Counted # CPUs for use : 64                \n'
b'  -- Enc. Only              : True                 \n'
b'  -- Dec. Only              : False                 \n'
b'  -- Output Dir             : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output                 \n'
b'  -- Skip N-Frames          : 0                 \n'
b'  -- # Frames To Be Coded   : -1                 \n'
b'  -- Bitstream              : sfu-hw-BasketballPass_416x240_50_val_qp24.bin                 \n'
b' Dataset                    : sfu-hw-BasketballPass_416x240_50_val                 \n'
b'  -- Data                   : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/images                 \n'
b'  -- Annotation             : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/annotations/BasketballPass_416x240_50_val.json                 \n'
b'  -- SEQ-INFO               : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BasketballPass_416x240_50_val/seqinfo.ini                         \n'
b' Evaluator                  : COCOEVal                                      \n'
b'  -- DataCatalog            : SFUHW                 \n'
b'  -- Output Dir             : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/evaluation                 \n'
b'  -- Output file            : COCOEVal_on_SFUHW_sfu-hw-BasketballPass_416x240_50_val         \n'
b'\n'
b'\n'
b'\r  0%|          | 0/97 [00:00<?, ?it/s]\r  1%|          | 1/97 [00:00<00:41,  2.33it/s]\r  2%|\xe2\x96\x8f         | 2/97 [00:00<00:23,  4.08it/s]\r  3%|\xe2\x96\x8e         | 3/97 [00:00<00:17,  5.37it/s]\r  4%|\xe2\x96\x8d         | 4/97 [00:00<00:14,  6.37it/s]\r  5%|\xe2\x96\x8c         | 5/97 [00:00<00:12,  7.12it/s]\r  6%|\xe2\x96\x8c         | 6/97 [00:00<00:11,  7.67it/s]\r  7%|\xe2\x96\x8b         | 7/97 [00:01<00:11,  8.07it/s]\r  8%|\xe2\x96\x8a         | 8/97 [00:01<00:10,  8.35it/s]\r  9%|\xe2\x96\x89         | 9/97 [00:01<00:10,  8.55it/s]\r 10%|\xe2\x96\x88         | 10/97 [00:01<00:10,  8.69it/s]\r 11%|\xe2\x96\x88\xe2\x96\x8f        | 11/97 [00:01<00:09,  8.79it/s]\r 12%|\xe2\x96\x88\xe2\x96\x8f        | 12/97 [00:01<00:09,  8.78it/s]\r 13%|\xe2\x96\x88\xe2\x96\x8e        | 13/97 [00:01<00:09,  8.82it/s]\r 14%|\xe2\x96\x88\xe2\x96\x8d        | 14/97 [00:01<00:09,  8.85it/s]\r 15%|\xe2\x96\x88\xe2\x96\x8c        | 15/97 [00:02<00:09,  8.85it/s]\r 16%|\xe2\x96\x88\xe2\x96\x8b        | 16/97 [00:02<00:09,  8.88it/s]\r 18%|\xe2\x96\x88\xe2\x96\x8a        | 17/97 [00:02<00:08,  8.91it/s]\r 19%|\xe2\x96\x88\xe2\x96\x8a        | 18/97 [00:02<00:08,  8.94it/s]\r 20%|\xe2\x96\x88\xe2\x96\x89        | 19/97 [00:02<00:08,  8.96it/s]\r 21%|\xe2\x96\x88\xe2\x96\x88        | 20/97 [00:02<00:08,  8.96it/s]\r 22%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f       | 21/97 [00:02<00:08,  8.96it/s]\r 23%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e       | 22/97 [00:02<00:08,  8.97it/s]\r 24%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e       | 23/97 [00:02<00:08,  8.97it/s]\r 25%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d       | 24/97 [00:03<00:08,  8.96it/s]\r 26%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c       | 25/97 [00:03<00:08,  8.96it/s]\r 27%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b       | 26/97 [00:03<00:07,  8.97it/s]\r 28%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a       | 27/97 [00:03<00:07,  8.99it/s]\r 29%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89       | 28/97 [00:03<00:07,  8.99it/s]\r 30%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89       | 29/97 [00:03<00:07,  9.00it/s]\r 31%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88       | 30/97 [00:03<00:07,  8.91it/s]\r 32%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f      | 31/97 [00:03<00:07,  8.93it/s]\r 33%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e      | 32/97 [00:03<00:07,  8.95it/s]\r 34%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d      | 33/97 [00:04<00:07,  8.96it/s]\r 35%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c      | 34/97 [00:04<00:07,  8.99it/s]\r 36%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c      | 35/97 [00:04<00:06,  8.98it/s]\r 37%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b      | 36/97 [00:04<00:06,  8.98it/s]\r 38%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a      | 37/97 [00:04<00:06,  8.97it/s]\r 39%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89      | 38/97 [00:04<00:06,  8.97it/s]\r 40%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88      | 39/97 [00:04<00:06,  8.98it/s]\r 41%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88      | 40/97 [00:04<00:06,  8.98it/s]\r 42%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f     | 41/97 [00:04<00:06,  8.98it/s]\r 43%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e     | 42/97 [00:05<00:06,  8.96it/s]\r 44%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d     | 43/97 [00:05<00:06,  8.96it/s]\r 45%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c     | 44/97 [00:05<00:05,  8.97it/s]\r 46%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b     | 45/97 [00:05<00:05,  8.97it/s]\r 47%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b     | 46/97 [00:05<00:05,  8.98it/s]\r 48%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a     | 47/97 [00:05<00:05,  8.97it/s]\r 49%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89     | 48/97 [00:05<00:05,  8.88it/s]\r 51%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88     | 49/97 [00:05<00:05,  8.90it/s]\r 52%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f    | 50/97 [00:05<00:05,  8.94it/s]\r 53%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e    | 51/97 [00:06<00:05,  8.96it/s]\r 54%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e    | 52/97 [00:06<00:05,  8.98it/s]\r 55%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d    | 53/97 [00:06<00:04,  9.00it/s]\r 56%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c    | 54/97 [00:06<00:04,  9.01it/s]\r 57%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b    | 55/97 [00:06<00:04,  9.03it/s]\r 58%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a    | 56/97 [00:06<00:04,  9.04it/s]\r 59%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89    | 57/97 [00:06<00:04,  9.06it/s]\r 60%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89    | 58/97 [00:06<00:04,  9.06it/s]\r 61%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88    | 59/97 [00:06<00:04,  9.06it/s]\r 62%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f   | 60/97 [00:07<00:04,  9.06it/s]\r 63%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e   | 61/97 [00:07<00:03,  9.06it/s]\r 64%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d   | 62/97 [00:07<00:03,  9.06it/s]\r 65%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d   | 63/97 [00:07<00:03,  9.07it/s]\r 66%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c   | 64/97 [00:07<00:03,  9.07it/s]\r 67%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b   | 65/97 [00:07<00:03,  9.08it/s]\r 68%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a   | 66/97 [00:07<00:03,  8.98it/s]\r 69%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89   | 67/97 [00:07<00:03,  9.00it/s]\r 70%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88   | 68/97 [00:07<00:03,  9.03it/s]\r 71%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88   | 69/97 [00:08<00:03,  9.04it/s]\r 72%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f  | 70/97 [00:08<00:02,  9.05it/s]\r 73%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e  | 71/97 [00:08<00:02,  9.06it/s]\r 74%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d  | 72/97 [00:08<00:02,  9.07it/s]\r 75%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c  | 73/97 [00:08<00:02,  9.07it/s]\r 76%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b  | 74/97 [00:08<00:02,  9.08it/s]\r 77%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b  | 75/97 [00:08<00:02,  9.07it/s]\r 78%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a  | 76/97 [00:08<00:02,  9.08it/s]\r 79%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89  | 77/97 [00:08<00:02,  9.08it/s]\r 80%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88  | 78/97 [00:08<00:02,  9.08it/s]\r 81%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f | 79/97 [00:09<00:01,  9.08it/s]\r 82%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f | 80/97 [00:09<00:01,  9.08it/s]\r 84%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e | 81/97 [00:09<00:01,  9.08it/s]\r 85%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d | 82/97 [00:09<00:01,  9.08it/s]\r 86%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c | 83/97 [00:09<00:01,  9.08it/s]\r 87%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b | 84/97 [00:09<00:01,  9.07it/s]\r 88%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a | 85/97 [00:09<00:01,  8.99it/s]\r 89%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a | 86/97 [00:09<00:01,  9.01it/s]\r 90%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89 | 87/97 [00:09<00:01,  9.04it/s]\r 91%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88 | 88/97 [00:10<00:00,  9.05it/s]\r 92%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8f| 89/97 [00:10<00:00,  9.06it/s]\r 93%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8e| 90/97 [00:10<00:00,  9.06it/s]\r 94%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d| 91/97 [00:10<00:00,  9.06it/s]\r 95%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8d| 92/97 [00:10<00:00,  9.06it/s]\r 96%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8c| 93/97 [00:10<00:00,  9.06it/s]\r 97%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8b| 94/97 [00:10<00:00,  9.06it/s]\r 98%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x8a| 95/97 [00:10<00:00,  9.08it/s]\r 99%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x89| 96/97 [00:10<00:00,  9.10it/s]\r100%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88| 97/97 [00:11<00:00,  9.11it/s]\r100%|\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88\xe2\x96\x88| 97/97 [00:11<00:00,  8.73it/s][INFO] Found group_quantization_path in config: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=0\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.475756, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.961239\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.285669, 2.557004]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.865222, 4.027986]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.004158, 7.574434]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.448033, 14.595056]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 1 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 0:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.285669, 2.557004]\n'
b'  medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.865222, 4.027986]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.004158, 7.574434]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.448033, 14.595056]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 0 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0000\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=1\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.413041, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.843235\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.233681, 2.224517]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.351644, 4.088983]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.659791, 6.473611]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.717901, 13.555277]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 2 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 1:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.233681, 2.224517]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.351644, 4.088983]\n'
b'  high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.659791, 6.473611]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.717901, 13.555277]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 1 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0001\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=2\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.465569, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.984962\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.271020, 2.174667]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.747095, 4.383429]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.198620, 6.009540]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.181847, 13.259615]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 3 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 2:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.271020, 2.174667]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.747095, 4.383429]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.198620, 6.009540]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.181847, 13.259615]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 2 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0002\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=3\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.447540, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.907201\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.250509, 2.151725]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.315027, 4.764205]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 4 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.391421, 5.929472]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.172513, 12.480341]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 4 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 3:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.250509, 2.151725]\n'
b'  medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.315027, 4.764205]\n'
b'  high: 4 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.391421, 5.929472]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.172513, 12.480341]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 3 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0003\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=4\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.458200, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.883693\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.204109, 2.343403]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.749951, 4.259748]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.424435, 7.578068]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.677205, 12.096061]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 5 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 4:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.204109, 2.343403]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.749951, 4.259748]\n'
b'  high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.424435, 7.578068]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.677205, 12.096061]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 4 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0004\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=5\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.401340, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.871334\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.224252, 2.342660]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 48 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.743351, 3.908748]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 4 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.311227, 5.354626]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.147772, 14.201153]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 6 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 5:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.224252, 2.342660]\n'
b'  medium_high: 48 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.743351, 3.908748]\n'
b'  high: 4 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.311227, 5.354626]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.147772, 14.201153]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 5 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0005\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=6\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.386008, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.867284\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.198584, 2.651018]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.852371, 4.591029]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.106120, 5.482970]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.872967, 13.551809]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 7 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 6:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.198584, 2.651018]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.852371, 4.591029]\n'
b'  high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.106120, 5.482970]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.872967, 13.551809]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 6 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0006\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=7\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.408411, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.808753\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.836297, 2.567432]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.243580, 4.900817]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.652969, 7.453187]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.546087, 14.333742]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 8 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 7:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.836297, 2.567432]\n'
b'  medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.243580, 4.900817]\n'
b'  high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.652969, 7.453187]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.546087, 14.333742]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 7 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0007\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=8\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.416421, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.892948\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.071511, 2.250733]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.640098, 4.319808]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.578003, 7.485005]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.410095, 11.756379]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 9 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 8:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.071511, 2.250733]\n'
b'  medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.640098, 4.319808]\n'
b'  high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.578003, 7.485005]\n'
b'  ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.410095, 11.756379]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 8 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0008\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=9\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.409310, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.738273\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.163662, 2.158755]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.286044, 4.190621]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.744799, 7.157575]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.033746, 10.962161]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 10 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 9:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.163662, 2.158755]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.286044, 4.190621]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.744799, 7.157575]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.033746, 10.962161]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 9 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0009\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=10\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.375656, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.866121\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.312734, 2.234522]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.817832, 4.108676]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.434098, 6.313640]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.865520, 13.094820]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 11 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 10:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.312734, 2.234522]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.817832, 4.108676]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.434098, 6.313640]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.865520, 13.094820]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 10 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0010\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=11\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.445674, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.015577\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.416758, 2.045064]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.921464, 4.386249]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.719456, 7.541795]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.292304, 14.073711]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 12 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 11:\n'
b'  medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.416758, 2.045064]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.921464, 4.386249]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.719456, 7.541795]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.292304, 14.073711]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 11 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0011\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=12\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.518087, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.932108\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.418252, 2.296299]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.466739, 4.332146]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.500818, 7.156081]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.933724, 11.194288]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 13 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 12:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.418252, 2.296299]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.466739, 4.332146]\n'
b'  high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.500818, 7.156081]\n'
b'  ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.933724, 11.194288]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 12 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0012\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=13\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.472142, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.894756\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.353473, 2.183412]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.903691, 4.295703]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.077127, 7.633726]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.615272, 11.363757]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 14 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 13:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.353473, 2.183412]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.903691, 4.295703]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.077127, 7.633726]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.615272, 11.363757]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 13 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0013\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=14\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.397458, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.783942\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.252880, 2.309455]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.476467, 4.318780]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.128106, 5.720930]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.963396, 11.980490]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 15 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 14:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.252880, 2.309455]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.476467, 4.318780]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.128106, 5.720930]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.963396, 11.980490]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 14 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0014\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=15\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.389896, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.732001\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.952364, 2.285535]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.964081, 5.098013]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.140963, 6.535787]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.614633, 11.669842]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 16 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 15:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.952364, 2.285535]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.964081, 5.098013]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.140963, 6.535787]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.614633, 11.669842]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 15 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0015\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=16\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.430191, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.789932\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.806994, 2.182881]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.540246, 4.236601]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.948436, 6.102515]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.021397, 12.137126]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 17 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 16:\n'
b'  medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.806994, 2.182881]\n'
b'  medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.540246, 4.236601]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.948436, 6.102515]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.021397, 12.137126]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 16 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0016\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=17\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.381886, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.763059\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.132063, 2.319660]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.136253, 3.879749]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.424596, 6.795785]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.405978, 12.370891]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 18 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 17:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.132063, 2.319660]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.136253, 3.879749]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.424596, 6.795785]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.405978, 12.370891]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 17 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0017\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=18\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.351372, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.837738\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.213589, 2.052032]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.726553, 3.592857]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.420907, 7.752028]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.266383, 13.790571]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 19 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 18:\n'
b'  medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.213589, 2.052032]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.726553, 3.592857]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.420907, 7.752028]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.266383, 13.790571]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 18 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0018\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=19\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.358214, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.932392\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.975972, 2.392356]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.192812, 3.814053]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.303611, 6.785087]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.238164, 14.192344]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 20 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 19:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.975972, 2.392356]\n'
b'  medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.192812, 3.814053]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.303611, 6.785087]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.238164, 14.192344]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 19 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0019\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=20\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.394129, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.807594\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.114702, 2.668943]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.197826, 3.922504]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.036897, 6.140636]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.938828, 11.972072]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 21 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 20:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.114702, 2.668943]\n'
b'  medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.197826, 3.922504]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.036897, 6.140636]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.938828, 11.972072]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 20 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0020\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=21\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.364514, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.727266\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.223534, 2.441000]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.544363, 4.028876]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.966163, 7.187496]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.177364, 10.780165]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 22 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 21:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.223534, 2.441000]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.544363, 4.028876]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.966163, 7.187496]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.177364, 10.780165]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 21 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0021\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=22\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.431611, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.897933\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.172096, 1.999603]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.646833, 3.852999]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.348186, 6.136173]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.487527, 11.357785]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 23 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 22:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.172096, 1.999603]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.646833, 3.852999]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.348186, 6.136173]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.487527, 11.357785]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 22 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0022\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=23\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.433907, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.894538\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 109 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.397918, 2.317712]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.100559, 4.031224]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.545894, 6.701066]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.798668, 12.809929]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 24 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 23:\n'
b'  medium_low: 109 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.397918, 2.317712]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.100559, 4.031224]\n'
b'  high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.545894, 6.701066]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.798668, 12.809929]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 23 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0023\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=24\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.399231, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.792226\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.199851, 2.019704]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.947292, 4.387443]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.106036, 6.238944]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.650425, 14.746758]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 25 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 24:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.199851, 2.019704]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.947292, 4.387443]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.106036, 6.238944]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.650425, 14.746758]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 24 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0024\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=25\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.504323, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.021978\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.315627, 2.592310]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.444742, 4.337336]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.010500, 5.813828]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.396388, 14.021018]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 26 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 25:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.315627, 2.592310]\n'
b'  medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.444742, 4.337336]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.010500, 5.813828]\n'
b'  ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.396388, 14.021018]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 25 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0025\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=26\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.451607, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.028804\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 109 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.354815, 2.580917]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 36 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.067675, 3.910852]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.094160, 5.063061]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.460275, 11.880858]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 27 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 26:\n'
b'  medium_low: 109 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.354815, 2.580917]\n'
b'  medium_high: 36 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.067675, 3.910852]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.094160, 5.063061]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.460275, 11.880858]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 26 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0026\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=27\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.533532, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.990596\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.179513, 2.230015]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.882504, 4.291173]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.219278, 5.650604]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.896467, 12.262419]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 28 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 27:\n'
b'  medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.179513, 2.230015]\n'
b'  medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.882504, 4.291173]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.219278, 5.650604]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.896467, 12.262419]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 27 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0027\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=28\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.697390, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.239228\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 112 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.220281, 2.363293]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 32 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.204311, 4.808803]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.292943, 6.795938]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.133193, 15.052434]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 29 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 28:\n'
b'  medium_low: 112 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.220281, 2.363293]\n'
b'  medium_high: 32 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.204311, 4.808803]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.292943, 6.795938]\n'
b'  ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.133193, 15.052434]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 28 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0028\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=29\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.703861, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.214894\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.166297, 2.378469]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.697035, 4.562268]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.894838, 6.797924]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.300209, 16.088049]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 30 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 29:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.166297, 2.378469]\n'
b'  medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.697035, 4.562268]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.894838, 6.797924]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.300209, 16.088049]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 29 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0029\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=30\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.686643, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.101842\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.309622, 2.302994]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.864475, 4.830485]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.252782, 6.081997]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.177405, 14.127984]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 31 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 30:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.309622, 2.302994]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.864475, 4.830485]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.252782, 6.081997]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.177405, 14.127984]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 30 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0030\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=31\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.641177, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.034177\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.076648, 2.159266]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.252866, 4.517607]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.415193, 6.061323]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.142290, 14.399947]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 32 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 31:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.076648, 2.159266]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.252866, 4.517607]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.415193, 6.061323]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.142290, 14.399947]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 31 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0031\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=32\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.571503, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.935944\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.396166, 2.451342]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.260221, 4.310861]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.810802, 6.419624]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.536121, 15.633724]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 33 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 32:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.396166, 2.451342]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.260221, 4.310861]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.810802, 6.419624]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.536121, 15.633724]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 32 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0032\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=33\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.636321, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.204808\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 97 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.300325, 2.080928]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 51 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.188032, 4.023059]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.700634, 7.156339]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.729364, 18.562269]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 34 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 33:\n'
b'  medium_low: 97 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.300325, 2.080928]\n'
b'  medium_high: 51 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.188032, 4.023059]\n'
b'  high: 5 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.700634, 7.156339]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.729364, 18.562269]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 33 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0033\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=34\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.560160, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.076380\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.479298, 2.455814]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.090772, 4.790176]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.797136, 6.065579]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.666686, 15.918475]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 35 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 34:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.479298, 2.455814]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.090772, 4.790176]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.797136, 6.065579]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.666686, 15.918475]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 34 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0034\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=35\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.544004, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.043346\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.031963, 2.194398]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.603164, 3.996566]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.448275, 6.103128]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.852324, 14.625421]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 36 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 35:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.031963, 2.194398]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.603164, 3.996566]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.448275, 6.103128]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.852324, 14.625421]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 35 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0035\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=36\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.657209, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.159217\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.704704, 2.622864]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.570665, 4.732169]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.540270, 6.250623]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.050858, 14.755432]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 37 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 36:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.704704, 2.622864]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.570665, 4.732169]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.540270, 6.250623]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.050858, 14.755432]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 36 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0036\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=37\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.663186, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.182179\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.194575, 2.728062]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.072083, 4.299956]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.047130, 7.758464]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.094840, 14.003868]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 38 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 37:\n'
b'  medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.194575, 2.728062]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.072083, 4.299956]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.047130, 7.758464]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.094840, 14.003868]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 37 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0037\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=38\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.692926, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.109434\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.007358, 2.451482]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.936154, 4.127124]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.105543, 6.818322]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.766740, 14.292905]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 39 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 38:\n'
b'  medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.007358, 2.451482]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.936154, 4.127124]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.105543, 6.818322]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.766740, 14.292905]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 38 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0038\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=39\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.717079, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.199420\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.459207, 2.416564]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.419207, 4.163160]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.279671, 5.713705]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.164853, 15.212030]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 40 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 39:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.459207, 2.416564]\n'
b'  medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.419207, 4.163160]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.279671, 5.713705]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.164853, 15.212030]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 39 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0039\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=40\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.670081, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.202224\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.339717, 2.436437]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.556867, 4.294627]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.411099, 6.746863]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.479113, 15.673989]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 41 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 40:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.339717, 2.436437]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.556867, 4.294627]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.411099, 6.746863]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.479113, 15.673989]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 40 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0040\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=41\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.598105, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.038109\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.196246, 2.368154]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.162168, 5.090642]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.357712, 7.519079]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.467082, 15.515504]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 42 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 41:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.196246, 2.368154]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.162168, 5.090642]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.357712, 7.519079]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.467082, 15.515504]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 41 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0041\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=42\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.683984, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.099480\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.291049, 2.258365]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.150585, 4.807383]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.912636, 7.310724]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.624674, 15.120342]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 43 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 42:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.291049, 2.258365]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.150585, 4.807383]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.912636, 7.310724]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.624674, 15.120342]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 42 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0042\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=43\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.682606, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.129602\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.365492, 2.464581]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.787956, 4.226676]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.622164, 7.181911]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.749441, 14.185590]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 44 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 43:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.365492, 2.464581]\n'
b'  medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.787956, 4.226676]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.622164, 7.181911]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.749441, 14.185590]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 43 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0043\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=44\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.564195, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.092009\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.576118, 2.263856]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.515804, 4.364941]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.775226, 7.009575]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.959459, 17.184696]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 45 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 44:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.576118, 2.263856]\n'
b'  medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.515804, 4.364941]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.775226, 7.009575]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.959459, 17.184696]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 44 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0044\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=45\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.598928, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.048765\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.375472, 2.549101]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.280753, 4.973475]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.323136, 7.926936]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.141620, 15.603520]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 46 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 45:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.375472, 2.549101]\n'
b'  medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.280753, 4.973475]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.323136, 7.926936]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.141620, 15.603520]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 45 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0045\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=46\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.737060, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.102824\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.302162, 2.648859]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.950625, 5.152447]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.103820, 7.527664]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.038859, 13.951056]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 47 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 46:\n'
b'  medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.302162, 2.648859]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.950625, 5.152447]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.103820, 7.527664]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.038859, 13.951056]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 46 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0046\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=47\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.724740, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.270845\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.470097, 2.689872]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.147870, 4.616783]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.376818, 7.914711]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.946984, 16.418661]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 48 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 47:\n'
b'  medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.470097, 2.689872]\n'
b'  medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.147870, 4.616783]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.376818, 7.914711]\n'
b'  ultra_high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.946984, 16.418661]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 47 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0047\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=48\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.947523, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.338226\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.420135, 2.560082]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.639739, 4.763231]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 13 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.451989, 6.660616]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.386527, 14.288899]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 49 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 48:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.420135, 2.560082]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.639739, 4.763231]\n'
b'  high: 13 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.451989, 6.660616]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.386527, 14.288899]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 48 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0048\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=49\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.978186, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.402164\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.778499, 2.517153]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.089369, 5.603374]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 13 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.686205, 7.705499]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.820741, 16.620598]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 50 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 49:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.778499, 2.517153]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.089369, 5.603374]\n'
b'  high: 13 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.686205, 7.705499]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.820741, 16.620598]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 49 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0049\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=50\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.903220, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.276432\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.490560, 2.803644]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.995754, 4.708898]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.539085, 7.420959]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.227659, 14.110733]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 51 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 50:\n'
b'  medium_low: 100 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.490560, 2.803644]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.995754, 4.708898]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.539085, 7.420959]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.227659, 14.110733]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 50 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0050\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=51\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.850813, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.098998\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.237786, 2.680666]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.846094, 4.921778]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.737482, 6.815934]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.421443, 12.248476]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 52 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 51:\n'
b'  medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.237786, 2.680666]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.846094, 4.921778]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.737482, 6.815934]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.421443, 12.248476]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 51 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0051\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=52\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.860293, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.113055\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 98 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.656494, 2.659039]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.612391, 5.071918]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.617853, 8.341937]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.798171, 13.081418]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 53 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 52:\n'
b'  medium_low: 98 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.656494, 2.659039]\n'
b'  medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.612391, 5.071918]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.617853, 8.341937]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.798171, 13.081418]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 52 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0052\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=53\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.766085, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.078533\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 98 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.411674, 2.745058]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.059178, 5.035714]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.538836, 7.719978]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.651934, 13.786676]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 54 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 53:\n'
b'  medium_low: 98 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.411674, 2.745058]\n'
b'  medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.059178, 5.035714]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.538836, 7.719978]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.651934, 13.786676]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 53 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0053\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=54\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.686095, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.937434\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 96 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.215662, 2.594914]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 48 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.575147, 5.141128]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.944030, 7.159767]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.185806, 14.502321]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 55 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 54:\n'
b'  medium_low: 96 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.215662, 2.594914]\n'
b'  medium_high: 48 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.575147, 5.141128]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.944030, 7.159767]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.185806, 14.502321]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 54 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0054\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=55\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.731737, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.987938\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 97 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.653199, 2.469526]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.684062, 4.813432]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 14 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.076778, 7.082918]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.428899, 13.107904]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 56 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 55:\n'
b'  medium_low: 97 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.653199, 2.469526]\n'
b'  medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.684062, 4.813432]\n'
b'  high: 14 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.076778, 7.082918]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.428899, 13.107904]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 55 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0055\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=56\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.501782, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.873791\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.358461, 2.251835]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.461277, 4.388257]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.861946, 6.693501]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.570385, 13.905044]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 57 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 56:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.358461, 2.251835]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.461277, 4.388257]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.861946, 6.693501]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.570385, 13.905044]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 56 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0056\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=57\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.517046, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.938550\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.217239, 2.384042]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.669286, 5.114670]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.843238, 6.555206]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.849662, 14.257625]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 58 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 57:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.217239, 2.384042]\n'
b'  medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.669286, 5.114670]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.843238, 6.555206]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.849662, 14.257625]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 57 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0057\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=58\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.628222, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.159316\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.426012, 2.574689]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.014030, 5.171937]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.004314, 7.749534]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.031883, 15.229172]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 59 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 58:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.426012, 2.574689]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.014030, 5.171937]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.004314, 7.749534]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.031883, 15.229172]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 58 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0058\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=59\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.609432, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.215716\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.253746, 2.258935]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.559710, 5.297453]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.403062, 6.011378]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-15.041364, 16.531998]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 60 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 59:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.253746, 2.258935]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.559710, 5.297453]\n'
b'  high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.403062, 6.011378]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-15.041364, 16.531998]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 59 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0059\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=60\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.699258, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.318247\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.262500, 2.205421]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.495838, 4.791022]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.743371, 8.541990]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-15.392336, 18.204325]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 61 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 60:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.262500, 2.205421]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.495838, 4.791022]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.743371, 8.541990]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-15.392336, 18.204325]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 60 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0060\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=61\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.648974, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.132504\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.062712, 2.490234]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.153569, 5.210295]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.230708, 7.751581]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.866526, 14.738320]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 62 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 61:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.062712, 2.490234]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.153569, 5.210295]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.230708, 7.751581]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.866526, 14.738320]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 61 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0061\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=62\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.658096, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.237776\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.255406, 2.446726]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.745643, 4.582191]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.221989, 7.950540]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.207784, 14.879003]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 63 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 62:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.255406, 2.446726]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.745643, 4.582191]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.221989, 7.950540]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.207784, 14.879003]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 62 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0062\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=63\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.479822, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.972717\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.118882, 2.095426]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.662987, 5.009880]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.027623, 7.551031]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.263911, 12.924335]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 64 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 63:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.118882, 2.095426]\n'
b'  medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.662987, 5.009880]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.027623, 7.551031]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.263911, 12.924335]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 63 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0063\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=64\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.485045, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.042210\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.103149, 1.894316]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.515446, 4.694334]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.973321, 6.776964]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.322525, 15.190835]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 65 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 64:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.103149, 1.894316]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.515446, 4.694334]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.973321, 6.776964]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.322525, 15.190835]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 64 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0064\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=65\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.521367, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.100659\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.018717, 1.861238]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.971333, 4.261349]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.214099, 7.193464]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.677713, 15.074683]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 66 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 65:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.018717, 1.861238]\n'
b'  medium_high: 44 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.971333, 4.261349]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.214099, 7.193464]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.677713, 15.074683]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 65 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0065\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=66\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.424707, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.992372\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.274126, 2.023903]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.776175, 3.736253]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.022082, 7.014973]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.414794, 15.390879]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 67 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 66:\n'
b'  medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.274126, 2.023903]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.776175, 3.736253]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.022082, 7.014973]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.414794, 15.390879]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 66 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0066\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=67\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.453020, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.991147\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.142870, 2.213792]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.305629, 4.106082]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.452415, 7.270881]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-14.804788, 14.523304]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 68 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 67:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.142870, 2.213792]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.305629, 4.106082]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.452415, 7.270881]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-14.804788, 14.523304]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 67 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0067\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=68\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.475324, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.045672\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.175402, 2.241367]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.559495, 4.496891]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.190711, 7.310823]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.984705, 14.118338]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 69 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 68:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.175402, 2.241367]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.559495, 4.496891]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.190711, 7.310823]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.984705, 14.118338]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 68 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0068\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=69\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.458389, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.049991\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.189254, 2.074664]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 49 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.926845, 4.233512]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.886181, 7.667631]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.801742, 14.029297]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 70 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 69:\n'
b'  medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.189254, 2.074664]\n'
b'  medium_high: 49 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.926845, 4.233512]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.886181, 7.667631]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.801742, 14.029297]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 69 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0069\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=70\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.513019, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.211583\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.357429, 2.180840]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.995147, 4.052736]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.192694, 7.608266]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.045501, 14.418758]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 71 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 70:\n'
b'  medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.357429, 2.180840]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.995147, 4.052736]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.192694, 7.608266]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.045501, 14.418758]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 70 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0070\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=71\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.500491, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.133715\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.235258, 2.388395]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.586505, 4.961011]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.837631, 6.993231]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.526449, 14.185439]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 72 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 71:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.235258, 2.388395]\n'
b'  medium_high: 45 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.586505, 4.961011]\n'
b'  high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.837631, 6.993231]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.526449, 14.185439]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 71 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0071\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=72\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.487931, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.035272\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.177754, 2.256539]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.693396, 4.445737]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.463815, 6.326375]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.312001, 13.154744]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 73 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 72:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.177754, 2.256539]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.693396, 4.445737]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.463815, 6.326375]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.312001, 13.154744]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 72 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0072\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=73\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.451171, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.945152\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.298341, 2.344744]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.873048, 3.723734]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.964749, 6.693758]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.239037, 13.094714]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 74 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 73:\n'
b'  medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.298341, 2.344744]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.873048, 3.723734]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.964749, 6.693758]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.239037, 13.094714]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 73 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0073\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=74\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.498720, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.081798\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.277003, 2.322534]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.053566, 4.394760]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.599040, 6.471261]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.543509, 12.000427]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 75 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 74:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.277003, 2.322534]\n'
b'  medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.053566, 4.394760]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.599040, 6.471261]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.543509, 12.000427]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 74 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0074\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=75\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.302598, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.904930\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.036696, 2.209532]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.697140, 3.841743]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.818776, 6.254713]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.479024, 13.352650]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 76 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 75:\n'
b'  medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.036696, 2.209532]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.697140, 3.841743]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.818776, 6.254713]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.479024, 13.352650]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 75 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0075\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=76\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.346885, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.930593\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.189430, 1.997601]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.366506, 5.351182]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.382533, 7.342582]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.819498, 14.390984]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 77 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 76:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.189430, 1.997601]\n'
b'  medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.366506, 5.351182]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.382533, 7.342582]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.819498, 14.390984]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 76 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0076\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=77\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.344415, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.963569\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.231692, 1.995266]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.276510, 4.349300]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.446864, 6.779632]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.926634, 13.917399]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 78 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 77:\n'
b'  medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.231692, 1.995266]\n'
b'  medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.276510, 4.349300]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.446864, 6.779632]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.926634, 13.917399]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 77 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0077\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=78\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.402704, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.973748\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.015154, 2.042551]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.543300, 4.109523]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.871981, 6.602652]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.535766, 14.389647]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 79 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 78:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.015154, 2.042551]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.543300, 4.109523]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.871981, 6.602652]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.535766, 14.389647]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 78 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0078\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=79\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.526669, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=4.117944\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.201582, 2.303114]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.464649, 4.412832]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.217319, 6.588966]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.290879, 13.624786]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 80 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 79:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.201582, 2.303114]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.464649, 4.412832]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.217319, 6.588966]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.290879, 13.624786]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 79 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0079\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=80\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.385128, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.995330\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.015949, 2.235564]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.338725, 4.739517]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-7.360786, 6.413567]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.790943, 13.362458]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 81 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 80:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.015949, 2.235564]\n'
b'  medium_high: 42 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.338725, 4.739517]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-7.360786, 6.413567]\n'
b'  ultra_high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.790943, 13.362458]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 80 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0080\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=81\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.268657, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.852327\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.141824, 1.933408]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.882052, 4.635500]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.079116, 6.906341]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.056157, 15.309750]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 82 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 81:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.141824, 1.933408]\n'
b'  medium_high: 47 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.882052, 4.635500]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.079116, 6.906341]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.056157, 15.309750]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 81 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0081\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=82\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.339587, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.975296\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.917090, 2.496542]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.168509, 3.900229]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.148347, 6.520994]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.438932, 14.883327]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 83 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 82:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.917090, 2.496542]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.168509, 3.900229]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.148347, 6.520994]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.438932, 14.883327]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 82 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0082\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=83\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.308775, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.759486\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.153780, 2.192659]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.825631, 4.624778]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.151371, 7.198336]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.049256, 14.361876]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 84 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 83:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.153780, 2.192659]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.825631, 4.624778]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.151371, 7.198336]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.049256, 14.361876]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 83 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0083\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=84\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.143779, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.654490\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.032857, 2.166288]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.238939, 4.623488]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.498482, 7.311888]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.515559, 12.516146]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 85 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 84:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.032857, 2.166288]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.238939, 4.623488]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.498482, 7.311888]\n'
b'  ultra_high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.515559, 12.516146]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 84 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0084\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=85\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.333276, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.770243\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.496074, 2.409684]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.681226, 4.559074]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.073433, 6.581489]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.578425, 13.669160]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 86 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 85:\n'
b'  medium_low: 102 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.496074, 2.409684]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.681226, 4.559074]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.073433, 6.581489]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.578425, 13.669160]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 85 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0085\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=86\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.241758, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.642434\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.070974, 2.109658]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.265021, 4.578018]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.947375, 6.523174]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.311739, 10.925947]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 87 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 86:\n'
b'  medium_low: 99 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.070974, 2.109658]\n'
b'  medium_high: 46 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.265021, 4.578018]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.947375, 6.523174]\n'
b'  ultra_high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.311739, 10.925947]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 86 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0086\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=87\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.226239, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.605055\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.936975, 2.083555]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.769048, 4.986546]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.534073, 5.620663]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.175746, 12.791657]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 88 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 87:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.936975, 2.083555]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.769048, 4.986546]\n'
b'  high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.534073, 5.620663]\n'
b'  ultra_high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.175746, 12.791657]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 87 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0087\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=88\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.206280, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.721943\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.410182, 2.461798]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.667161, 4.987032]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.938259, 5.419719]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.239321, 12.919699]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 89 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 88:\n'
b'  medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.410182, 2.461798]\n'
b'  medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.667161, 4.987032]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.938259, 5.419719]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.239321, 12.919699]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 88 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0088\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=89\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.099483, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.613588\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.991861, 1.974500]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.584211, 5.074464]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.606750, 5.318058]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.729713, 11.607199]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 90 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 89:\n'
b'  medium_low: 104 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.991861, 1.974500]\n'
b'  medium_high: 41 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.584211, 5.074464]\n'
b'  high: 10 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.606750, 5.318058]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.729713, 11.607199]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 89 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0089\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=90\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.051759, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.662077\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.033555, 1.808058]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.610047, 3.891594]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.269313, 5.050755]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-13.325062, 13.225508]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 91 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 90:\n'
b'  medium_low: 103 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.033555, 1.808058]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.610047, 3.891594]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.269313, 5.050755]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-13.325062, 13.225508]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 90 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0090\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=91\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.036739, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.462733\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.880211, 2.332597]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.541061, 4.436289]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.976856, 6.078319]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.306790, 11.453826]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 92 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 91:\n'
b'  medium_low: 101 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.880211, 2.332597]\n'
b'  medium_high: 43 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.541061, 4.436289]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.976856, 6.078319]\n'
b'  ultra_high: 8 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.306790, 11.453826]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 91 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0091\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=92\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.066692, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.415918\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.984748, 2.002223]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-3.853207, 4.402554]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.444689, 6.680714]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.407486, 10.346763]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 93 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 92:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.984748, 2.002223]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-3.853207, 4.402554]\n'
b'  high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.444689, 6.680714]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.407486, 10.346763]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 92 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0092\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=93\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.107737, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.570987\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.881364, 2.240197]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.003785, 3.622415]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.772831, 6.044976]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-12.133058, 11.412643]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 94 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 93:\n'
b'  medium_low: 106 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.881364, 2.240197]\n'
b'  medium_high: 37 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.003785, 3.622415]\n'
b'  high: 12 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.772831, 6.044976]\n'
b'  ultra_high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-12.133058, 11.412643]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 93 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0093\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=94\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.130703, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.681327\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.029299, 2.471072]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.009273, 4.138700]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.365054, 6.658841]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.986802, 11.691207]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 95 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 94:\n'
b'  medium_low: 105 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.029299, 2.471072]\n'
b'  medium_high: 39 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.009273, 4.138700]\n'
b'  high: 9 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.365054, 6.658841]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.986802, 11.691207]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 94 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0094\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=95\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.188277, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.725321\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-2.282093, 2.420366]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.045177, 4.282322]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-6.258288, 6.192797]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-10.381772, 13.027253]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 96 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 95:\n'
b'  medium_low: 108 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-2.282093, 2.420366]\n'
b'  medium_high: 38 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.045177, 4.282322]\n'
b'  high: 7 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-6.258288, 6.192797]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-10.381772, 13.027253]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 95 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0095\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] tensor_to_frame called, sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'[DEBUG] \xe4\xbd\xbf\xe7\x94\xa8\xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe6\xa8\xa1\xe5\xbc\x8f\n'
b'[DEBUG] group_quantization_analysis called, group_info_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data, frame_index=96\n'
b'[DEBUG] \xe5\x8a\xa8\xe6\x80\x81\xe8\x8c\x83\xe5\x9b\xb4\xe7\xbb\x9f\xe8\xae\xa1: \xe5\x9d\x87\xe5\x80\xbc=3.349762, \xe6\xa0\x87\xe5\x87\x86\xe5\xb7\xae=3.953187\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-1.980161, 2.654693]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-4.349691, 4.150173]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-5.132212, 7.497264]\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84 ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe5\x85\xa8\xe5\xb1\x80\xe8\x8c\x83\xe5\x9b\xb4=[-11.706594, 14.506211]\n'
b'[save_group_info] \xe4\xbf\x9d\xe5\xad\x98\xe7\xac\xac 97 \xe5\xb8\xa7\xe5\x88\x86\xe7\xbb\x84\xe4\xbf\xa1\xe6\x81\xaf\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/group_info.npy\n'
b'[\xe5\x88\x86\xe7\xbb\x84\xe7\xbb\x9f\xe8\xae\xa1\xe6\x91\x98\xe8\xa6\x81] \xe5\xb8\xa7 96:\n'
b'  medium_low: 107 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-1.980161, 2.654693]\n'
b'  medium_high: 40 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-4.349691, 4.150173]\n'
b'  high: 6 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-5.132212, 7.497264]\n'
b'  ultra_high: 11 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93, \xe8\x8c\x83\xe5\x9b\xb4=[-11.706594, 14.506211]\n'
b'[EXPORT] \xe5\xaf\xbc\xe5\x87\xba\xe5\xb8\xa7 96 \xe7\x9a\x84\xe7\x9c\x9f\xe5\xae\x9e\xe6\x95\xb0\xe6\x8d\xae\xe5\x88\xb0: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data/exported_real_data/frame_0096\n'
b'[DEBUG] \xe5\x88\x86\xe7\xbb\x84\xe9\x87\x8f\xe5\x8c\x96\xe5\xae\x8c\xe6\x88\x90\xef\xbc\x8c\xe5\x85\xb1\xe5\xa4\x84\xe7\x90\x86 164 \xe4\xb8\xaa\xe9\x80\x9a\xe9\x81\x93\n'
b'[DEBUG] encode_ftensors: sidecar_path=None, group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/group_quantization_data\n'
b'Submitted job_id [001] completed\n'
b'Submitted job_id [000] completed\n'
b'[2025-07-28 23:30:52,625][root][WARNING] - Missing frames: expected POC up to 65, only got 33 frames.\n'
b'\n'
b'--> job_id [000] Running: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/bin/EncoderAppStatic -i /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400_input.yuv -c /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg -q 24 -o /dev/null -wdt 96 -hgt 464 -fr 50 -ts 1 -dph 0 --IntraPeriod=64 --InputChromaFormat=400 --InputBitDepth=10 --ConformanceWindowMode=1 --Level=5.1 --DecodingRefreshType=1 --BitstreamFile=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400-part-000.bin --FrameSkip=0 --FramesToBeEncoded=65\n'
b'--> job_id [001] Running: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/bin/EncoderAppStatic -i /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400_input.yuv -c /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg -q 24 -o /dev/null -wdt 96 -hgt 464 -fr 50 -ts 1 -dph 0 --IntraPeriod=64 --InputChromaFormat=400 --InputBitDepth=10 --ConformanceWindowMode=1 --Level=5.1 --DecodingRefreshType=1 --BitstreamFile=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400-part-001.bin --FrameSkip=64 --FramesToBeEncoded=33\n'
b'--> Running: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/bin/parcatStatic /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400-part-000.bin /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400-part-001.bin /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW/sfu-hw-BasketballPass_416x240_50_val/qp24/codec_output/sfu-hw-BasketballPass_416x240_50_val_qp24_96x464_50fps_10bit_p400.bin\n'
b'\n'
b"{0: {'enc_time': '12', 'bits': '99600'}, 1: {'enc_time': '12', 'bits': '28248'}, 2: {'enc_time': '15', 'bits': '43272'}, 3: {'enc_time': '13', 'bits': '26824'}, 4: {'enc_time': '17', 'bits': '37952'}, 5: {'enc_time': '14', 'bits': '28680'}, 6: {'enc_time': '16', 'bits': '41944'}, 7: {'enc_time': '14', 'bits': '25704'}, 8: {'enc_time': '21', 'bits': '68608'}, 9: {'enc_time': '12', 'bits': '28912'}, 10: {'enc_time': '12', 'bits': '38464'}, 11: {'enc_time': '15', 'bits': '29672'}, 12: {'enc_time': '15', 'bits': '41096'}, 13: {'enc_time': '14', 'bits': '31128'}, 14: {'enc_time': '16', 'bits': '40984'}, 15: {'enc_time': '15', 'bits': '29536'}, 16: {'enc_time': '20', 'bits': '68216'}, 17: {'enc_time': '15', 'bits': '30304'}, 18: {'enc_time': '18', 'bits': '42360'}, 19: {'enc_time': '15', 'bits': '28720'}, 20: {'enc_time': '17', 'bits': '46584'}, 21: {'enc_time': '18', 'bits': '30176'}, 22: {'enc_time': '20', 'bits': '42600'}, 23: {'enc_time': '17', 'bits': '32136'}, 24: {'enc_time': '21', 'bits': '66368'}, 25: {'enc_time': '15', 'bits': '27144'}, 26: {'enc_time': '18', 'bits': '43208'}, 27: {'enc_time': '16', 'bits': '27488'}, 28: {'enc_time': '18', 'bits': '42552'}, 29: {'enc_time': '16', 'bits': '30512'}, 30: {'enc_time': '18', 'bits': '40808'}, 31: {'enc_time': '14', 'bits': '27784'}, 32: {'enc_time': '22', 'bits': '65784'}}\n"
b'In encoded segment expected 65 but parsed 33 frames\n'
b'bitstreams generated, exiting\n'
b'\xe4\xbb\xbb\xe5\x8a\xa1\xe5\xae\x8c\xe6\x88\x90\n'



Traceback (most recent call last):
  File "../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py", line 403, in <module>
    output_df = generate_csv_classwise_video_map(
  File "../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py", line 156, in generate_csv_classwise_video_map
    results_df = read_df_rec(result_path, seq_list, nb_operation_points)
  File "../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py", line 70, in read_df_rec
    return pd.concat(
  File "/work/Users/<USER>/anaconda/envs/fctm7.0/lib/python3.8/site-packages/pandas/core/reshape/concat.py", line 372, in concat
    op = _Concatenator(
  File "/work/Users/<USER>/anaconda/envs/fctm7.0/lib/python3.8/site-packages/pandas/core/reshape/concat.py", line 429, in __init__
    raise ValueError("No objects to concatenate")
ValueError: No objects to concatenate
python3 ../../compressai_vision/scripts/metrics/gen_mpeg_cttc_csv.py -r /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_verify/SFUHW -dp /work/Users/<USER>/fcm_testdata/SFU_HW_Obj -dn SFU
***************
