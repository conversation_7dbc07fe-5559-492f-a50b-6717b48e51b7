- codec=compressai_vision_fctm.yaml
- ++pipeline.type=video
- ++paths._run_root=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
- ++vision_model.arch=faster_rcnn_X_101_32x8d_FPN_3x
- ++dataset.type=Detectron2Dataset
- ++dataset.datacatalog=SFUHW
- ++dataset.config.root=/work/Users/<USER>/fcm_testdata/SFU_HW_Obj/ParkScene_1920x1080_24_val
- ++dataset.config.annotation_file=annotations/ParkScene_1920x1080_24_val.json
- ++dataset.config.dataset_name=sfu-hw-ParkScene_1920x1080_24_val
- ++evaluator.type=COCO-EVAL
- ++evaluator.overwrite_results=True
- ++codec.experiment=group_SFU_test
- ++codec.enc_configs.frame_rate=24
- ++codec.enc_configs.intra_period=32
- ++codec.enc_configs.parallel_encoding=True
- ++codec.enc_configs.qp=12
- ++codec.tools.inner_codec.stash_outputs=False
- ++codec.tools.inner_codec.type=vtm
- ++codec.tools.inner_codec.codec_paths._root=/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3
- ++codec.tools.inner_codec.codec_paths.cfg_file=/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg
- ++codec.tools.feature_reduction.learned_model.split_ctx=obj
- ++codec.eval_encode=bitrate
- ++codec.verbosity=0
- ++codec.device.all=cuda
- ++misc.device.nn_parts=cuda
- ++pipeline.codec.group_quantization_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_test/SFUHW/sfu-hw-ParkScene_1920x1080_24_val/qp12/codec_output/group_quantization_data
