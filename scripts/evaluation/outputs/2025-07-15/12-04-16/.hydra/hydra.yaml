hydra:
  run:
    dir: outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}
  sweep:
    dir: multirun/${now:%Y-%m-%d}/${now:%H-%M-%S}
    subdir: ${hydra.job.num}
  launcher:
    _target_: hydra._internal.core_plugins.basic_launcher.BasicLauncher
  sweeper:
    _target_: hydra._internal.core_plugins.basic_sweeper.BasicSweeper
    max_batch_size: null
    params: null
  help:
    app_name: ${hydra.job.name}
    header: '${hydra.help.app_name} is powered by Hydra.

      '
    footer: 'Powered by Hydra (https://hydra.cc)

      Use --hydra-help to view Hydra specific help

      '
    template: '${hydra.help.header}

      == Configuration groups ==

      Compose your configuration from those groups (group=option)


      $APP_CONFIG_GROUPS


      == Config ==

      Override anything in the config (foo.bar=value)


      $CONFIG


      ${hydra.help.footer}

      '
  hydra_help:
    template: 'Hydra (${hydra.runtime.version})

      See https://hydra.cc for more info.


      == Flags ==

      $FLAGS_HELP


      == Configuration groups ==

      Compose your configuration from those groups (For example, append hydra/job_logging=disabled
      to command line)


      $HYDRA_CONFIG_GROUPS


      Use ''--cfg hydra'' to Show the Hydra config.

      '
    hydra_help: ???
  hydra_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][HYDRA] %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
    root:
      level: INFO
      handlers:
      - console
    loggers:
      logging_example:
        level: DEBUG
    disable_existing_loggers: false
  job_logging:
    version: 1
    formatters:
      simple:
        format: '[%(asctime)s][%(name)s][%(levelname)s] - %(message)s'
    handlers:
      console:
        class: logging.StreamHandler
        formatter: simple
        stream: ext://sys.stdout
      file:
        class: logging.FileHandler
        formatter: simple
        filename: ${hydra.runtime.output_dir}/${hydra.job.name}.log
    root:
      level: INFO
      handlers:
      - console
      - file
    disable_existing_loggers: false
  env: {}
  mode: RUN
  searchpath: []
  callbacks: {}
  output_subdir: .hydra
  overrides:
    hydra:
    - hydra.mode=RUN
    task:
    - codec=compressai_vision_fctm.yaml
    - ++pipeline.type=video
    - ++paths._run_root=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
    - ++vision_model.arch=faster_rcnn_X_101_32x8d_FPN_3x
    - ++dataset.type=Detectron2Dataset
    - ++dataset.datacatalog=SFUHW
    - ++dataset.config.root=/work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BQTerrace_1920x1080_60_val
    - ++dataset.config.annotation_file=annotations/BQTerrace_1920x1080_60_val.json
    - ++dataset.config.dataset_name=sfu-hw-BQTerrace_1920x1080_60_val
    - ++evaluator.type=COCO-EVAL
    - ++evaluator.overwrite_results=True
    - ++codec.experiment=fcm7.0_ori_SFU
    - ++codec.enc_configs.frame_rate=60
    - ++codec.enc_configs.intra_period=64
    - ++codec.enc_configs.parallel_encoding=True
    - ++codec.enc_configs.qp=10
    - ++codec.tools.inner_codec.stash_outputs=False
    - ++codec.tools.inner_codec.type=vtm
    - ++codec.tools.inner_codec.codec_paths._root=/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3
    - ++codec.tools.inner_codec.codec_paths.cfg_file=/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg
    - ++codec.tools.feature_reduction.learned_model.split_ctx=obj
    - ++codec.eval_encode=bitrate
    - ++codec.verbosity=0
    - ++codec.device.all=cuda
    - ++misc.device.nn_parts=cuda
  job:
    name: eval_split_inference
    chdir: null
    override_dirname: ++codec.device.all=cuda,++codec.enc_configs.frame_rate=60,++codec.enc_configs.intra_period=64,++codec.enc_configs.parallel_encoding=True,++codec.enc_configs.qp=10,++codec.eval_encode=bitrate,++codec.experiment=fcm7.0_ori_SFU,++codec.tools.feature_reduction.learned_model.split_ctx=obj,++codec.tools.inner_codec.codec_paths._root=/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3,++codec.tools.inner_codec.codec_paths.cfg_file=/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg,++codec.tools.inner_codec.stash_outputs=False,++codec.tools.inner_codec.type=vtm,++codec.verbosity=0,++dataset.config.annotation_file=annotations/BQTerrace_1920x1080_60_val.json,++dataset.config.dataset_name=sfu-hw-BQTerrace_1920x1080_60_val,++dataset.config.root=/work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BQTerrace_1920x1080_60_val,++dataset.datacatalog=SFUHW,++dataset.type=Detectron2Dataset,++evaluator.overwrite_results=True,++evaluator.type=COCO-EVAL,++misc.device.nn_parts=cuda,++paths._run_root=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output,++pipeline.type=video,++vision_model.arch=faster_rcnn_X_101_32x8d_FPN_3x,codec=compressai_vision_fctm.yaml
    id: ???
    num: ???
    config_name: eval_fctm.yaml
    env_set: {}
    env_copy: []
    config:
      override_dirname:
        kv_sep: '='
        item_sep: ','
        exclude_keys: []
  runtime:
    version: 1.3.2
    version_base: '1.3'
    cwd: /work/Users/<USER>/fctm-v7.0/scripts/evaluation
    config_sources:
    - path: hydra.conf
      schema: pkg
      provider: hydra
    - path: /work/Users/<USER>/fctm-v7.0/compressai_vision/cfgs
      schema: file
      provider: main
    - path: /work/Users/<USER>/fctm-v7.0/cfgs
      schema: file
      provider: command-line
    - path: ''
      schema: structured
      provider: schema
    output_dir: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/outputs/2025-07-15/12-04-16
    choices:
      codec: compressai_vision_fctm.yaml
      pipeline: split_inference
      vision_model: default
      evaluator: default
      dataset: default
      misc: default
      env: default
      paths: default
      hydra/env: default
      hydra/callbacks: null
      hydra/job_logging: default
      hydra/hydra_logging: default
      hydra/hydra_help: default
      hydra/help: default
      hydra/sweeper: basic
      hydra/launcher: basic
      hydra/output: default
  verbose: false
