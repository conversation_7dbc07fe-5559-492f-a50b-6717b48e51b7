paths:
  _common_root: ./logs
  _run_root: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
  configs: ${codec.output_dir}/configs
  src: ${codec.output_dir}/src
env:
  git:
    compressai:
      branch: null
      hash: null
      main_branch: master
      main_hash: null
    compressai_vision:
      branch: null
      hash: null
      main_branch: main
      main_hash: null
  slurm:
    account: null
    job_id: null
    job_array_task_id: null
    job_name: null
  system:
    hostname: null
    username: null
misc:
  device:
    nn_parts: cuda
    nn_part1: ${misc.device.nn_parts}
    nn_part2: ${misc.device.nn_parts}
  seed: 1234
dataset:
  type: Detectron2Dataset
  datacatalog: SFUHW
  config:
    root: /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/BQMall_832x480_60_val
    dataset_name: sfu-hw-BQMall_832x480_60_val
    imgs_folder: images
    annotation_file: annotations/BQMall_832x480_60_val.json
    seqinfo: seqinfo.ini
    ext: png
  settings:
    linear_mapper: false
    patch_size:
    - 512
    - 512
    ret_name: false
    use_BGR: false
  transforms:
  - Resize:
      size: ${....settings.patch_size}
  - ToTensor: {}
  loader:
    shuffle: false
    batch_size: 1
    num_workers: 2
evaluator:
  type: COCO-EVAL
  output_dir: ${pipeline.evaluation.evaluation_dir}
  overwrite_results: true
  eval_criteria: ''
vision_model:
  arch: faster_rcnn_X_101_32x8d_FPN_3x
  model_root_path: default
  faster_rcnn_R_50_FPN_3x:
    model_path_prefix: ${..model_root_path}
    cfg: models/detectron2/configs/COCO-Detection/faster_rcnn_R_50_FPN_3x.yaml
    weights: weights/detectron2/COCO-Detection/faster_rcnn_R_50_FPN_3x/137849458/model_final_280758.pkl
    splits: r2
  faster_rcnn_X_101_32x8d_FPN_3x:
    model_path_prefix: ${..model_root_path}
    cfg: models/detectron2/configs/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x.yaml
    weights: weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl
    splits: fpn
  mask_rcnn_R_50_FPN_3x:
    model_path_prefix: ${..model_root_path}
    cfg: models/detectron2/configs/COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x.yaml
    weights: weights/detectron2/COCO-InstanceSegmentation/mask_rcnn_R_50_FPN_3x/137849600/model_final_f10217.pkl
    splits: r2
  mask_rcnn_X_101_32x8d_FPN_3x:
    model_path_prefix: ${..model_root_path}
    cfg: models/detectron2/configs/COCO-InstanceSegmentation/mask_rcnn_X_101_32x8d_FPN_3x.yaml
    weights: weights/detectron2/COCO-InstanceSegmentation/mask_rcnn_X_101_32x8d_FPN_3x/139653917/model_final_2d9806.pkl
    splits: fpn
  jde_1088x608:
    model_path_prefix: ${..model_root_path}
    cfg: models/Towards-Realtime-MOT/cfg/yolov3_1088x608.cfg
    weights: weights/jde/jde.1088x608.uncertainty.pt
    iou_thres: 0.5
    conf_thres: 0.5
    nms_thres: 0.4
    min_box_area: 200
    track_buffer: 30
    frame_rate: 30
    splits:
    - 36
    - 61
    - 74
  yolox_darknet53:
    model_path_prefix: ${..model_root_path}
    cfg: Built-in configurations
    num_classes: 80
    conf_thres: 0.001
    nms_thres: 0.65
    weights: weights/yolox/darknet53/yolox_darknet.pth
    splits: l13
    squeeze_at_split: false
  rtmo_multi_person_pose_estimation:
    model_path_prefix: ${..model_root_path}
    cfg: models/mmpose/configs/body_2d_keypoint/rtmo/coco/rtmo-l_16xb16-600e_coco-640x640.py
    weights: weights/mmpose/rtmo_coco/rtmo-l_16xb16-600e_coco-640x640-516a421f_20231211.pth
    splits: backbone
pipeline:
  name: split-inference
  type: video
  output_dir_root: ${paths._run_root}/${.name}-${.type}
  datatype: float32
  nn_task_part1:
    load_features: false
    load_features_n_bits: -1
    load_features_when_available: false
    dump_features: false
    dump_features_n_bits: -1
    generate_features_only: false
    feature_dir: ${..output_dir_root}/features/${dataset.datacatalog}/${dataset.config.dataset_name}
  codec:
    encode_only: false
    decode_only: false
    codec_output_dir: ${codec.output_dir}/codec_output
    bitstream_name: ${codec.bitstream_name}
    skip_n_frames: 0
    n_frames_to_be_encoded: -1
    measure_complexity: ${codec.mac_computation}
    group_quantization_path: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctmgroup_SFU_test/SFUHW/sfu-hw-BQMall_832x480_60_val/qp22/codec_output/group_quantization_data
  nn_task_part2:
    dump_results: false
    output_results_dir: ${codec.output_dir}/output_results
  conformance:
    save_conformance_files: false
    subsample_ratio: 9
    conformance_files_path: ${codec.output_dir}/conformance_files/
  evaluation:
    bypass: false
    dump: true
    evaluation_dir: ${codec.output_dir}/evaluation
  visualization:
    save_visualization: ${codec.save_visualization}
    visualization_dir: ${codec.output_dir}/visualization
    threshold: 0
codec:
  type: fctm
  eval_encode: bitrate
  experiment: group_SFU_test
  output_dir: ${pipeline.output_dir_root}/${.type}${.experiment}/${dataset.datacatalog}/${dataset.config.dataset_name}/qp${codec.tools.inner_codec.enc_configs.qp}
  bitstream_name: ${dataset.config.dataset_name}_qp${codec.tools.inner_codec.enc_configs.qp}
  verbosity: 0
  coding_behaviour: all
  device:
    all: cuda
    enc: ${codec.device.all}
    dec: ${codec.device.all}
  mac_computation: false
  fcm_sample_stream_wrapper_enabled: true
  enc_configs:
    qp: 22
    frame_rate: 60
    intra_period: 64
    n_bit: 10
    parallel_encoding: true
    hash_check: 0
  tools:
    feature_transform:
      enabled: false
    feature_reduction:
      type: pre-trained
      learned_model:
        name: light_fedrnet
        selective_learning_strategy: false
        split_ctx: obj
      channel_removal:
        enabled: true
        period: ${codec.enc_configs.intra_period}
      temporal_resampling_enabled: false
    refinements:
      on_restored_ftensor:
        enabled: false
        period: ${codec.enc_configs.intra_period}
      on_reduced_ftensor:
        enabled: false
        period: ${codec.enc_configs.intra_period}
    conversion:
      type: tensor_packing
      n_bit: ${codec.enc_configs.n_bit}
      resized_channel_packing:
        enabled: true
        period: ${codec.enc_configs.intra_period}
    inner_codec:
      type: vtm
      stash_outputs: false
      inner_coding_max_chroma_format: 0
      inner_coding_max_pred_constraint: 1
      enc_configs: ${codec.enc_configs}
      codec_paths:
        _root: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3
        enc_exe: ${._root}/bin/EncoderAppStatic
        dec_exe: ${._root}/bin/DecoderAppStatic
        merge_exe: ${._root}/bin/parcatStatic
        cfg_file: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg
        default_cfg_file: ${._root}/cfg/encoder.cfg
