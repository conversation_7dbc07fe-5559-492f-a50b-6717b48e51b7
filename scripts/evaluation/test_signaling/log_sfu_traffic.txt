开始对 Traffic_2560x1600_30_val 进行编码 (QP=16)...
============================== RUNNING FCTM + COMPRESSAI-VISION ==================================
Datatset location:   /work/Users/<USER>/fcm_testdata
Output directory:    /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
Experiment folder:   fctm_original
Running Device:      cpu
Input sequence:      Traffic_2560x1600_30_val
Seq. Framerate:      30
QP for Inner Codec:  16
Intra Period for Inner Codec: 32
Other Parameters:    ++pipeline.codec.sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
==================================================================================================
fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision
fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision
fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision
fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision
fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision
[2025-06-11 11:16:52,841][fvcore.common.checkpoint][INFO] - [Checkpointer] Loading from /work/Users/<USER>/fctm-v7.0/compressai_vision/compressai_vision/model_wrappers/../../weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl ...
[2025-06-11 11:16:53,074][fvcore.common.checkpoint][INFO] - Reading a file from 'Detectron2 Model Zoo'
[2025-06-11 11:16:53,159][detectron2.data.datasets.coco][INFO] - Loaded 33 images in COCO format from /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/Traffic_2560x1600_30_val/annotations/Traffic_2560x1600_30_val.json
[2025-06-11 11:16:53,166][Detectron2Dataset][INFO] - "sfu-hw-Traffic_2560x1600_30_val" successfully registred.
[2025-06-11 11:16:53,166][detectron2.data.common][INFO] - Serializing 33 elements to byte tensors and concatenating them all ...
[2025-06-11 11:16:53,168][detectron2.data.common][INFO] - Serialized dataset takes 0.19 MiB
[2025-06-11 11:16:53,168][detectron2.data.dataset_mapper][INFO] - [DatasetMapper] Augmentations used in inference: [ResizeShortestEdge(short_edge_length=(800, 800), max_size=1333, sample_style='choice')]
[2025-06-11 11:16:53,169][detectron2.data.dataset_mapper][INFO] - [DatasetMapper] Augmentations used in inference: [ResizeShortestEdge(short_edge_length=(800, 800), max_size=1333, sample_style='choice')]
[2025-06-11 11:16:53,176][COCOEVal][INFO] - creating output folder: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/evaluation
fatal: not a git repository: /work/Users/<USER>/fctm-v7.0/compressai_vision/../.git/modules/compressai_vision
[2025-06-11 11:16:54,055][compressai_vision.run.eval_split_inference][INFO] -                 
 ============================================================                
 Pipeline                   : VideoSplitInference                           
 Vision Model               : faster_rcnn_X_101_32x8d_FPN_3x                
  -- Split Point            : ['p2', 'p3', 'p4', 'p5']                
  -- Cfg                    : /work/Users/<USER>/fctm-v7.0/models/detectron2/configs/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x.yaml                
  -- Weights                : /work/Users/<USER>/fctm-v7.0/weights/detectron2/COCO-Detection/faster_rcnn_X_101_32x8d_FPN_3x/139173657/model_final_68b088.pkl                
 Codec                      : FCTM                                          
  -- Counted # CPUs for use : 64                
  -- Enc. Only              : False                 
  -- Dec. Only              : False                 
  -- Output Dir             : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output                 
  -- Skip N-Frames          : 0                 
  -- # Frames To Be Coded   : -1                 
  -- Bitstream              : sfu-hw-Traffic_2560x1600_30_val_qp16.bin                 
 Dataset                    : sfu-hw-Traffic_2560x1600_30_val                 
  -- Data                   : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/Traffic_2560x1600_30_val/images                 
  -- Annotation             : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/Traffic_2560x1600_30_val/annotations/Traffic_2560x1600_30_val.json                 
  -- SEQ-INFO               : /work/Users/<USER>/fcm_testdata/SFU_HW_Obj/Traffic_2560x1600_30_val/seqinfo.ini                         
 Evaluator                  : COCOEVal                                      
  -- DataCatalog            : SFUHW                 
  -- Output Dir             : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/evaluation                 
  -- Output file            : COCOEVal_on_SFUHW_sfu-hw-Traffic_2560x1600_30_val         



  0%|          | 0/33 [00:00<?, ?it/s]
  3%|▎         | 1/33 [00:01<00:54,  1.71s/it]
  6%|▌         | 2/33 [00:03<00:46,  1.51s/it]
  9%|▉         | 3/33 [00:04<00:40,  1.34s/it]
 12%|█▏        | 4/33 [00:05<00:36,  1.26s/it]
 15%|█▌        | 5/33 [00:06<00:32,  1.18s/it]
 18%|█▊        | 6/33 [00:07<00:30,  1.13s/it]
 21%|██        | 7/33 [00:08<00:28,  1.08s/it]
 24%|██▍       | 8/33 [00:09<00:27,  1.10s/it]
 27%|██▋       | 9/33 [00:10<00:27,  1.14s/it]
 30%|███       | 10/33 [00:11<00:26,  1.16s/it]
 33%|███▎      | 11/33 [00:13<00:25,  1.16s/it]
 36%|███▋      | 12/33 [00:14<00:23,  1.12s/it]
 39%|███▉      | 13/33 [00:15<00:23,  1.17s/it]
 42%|████▏     | 14/33 [00:16<00:22,  1.18s/it]
 45%|████▌     | 15/33 [00:17<00:20,  1.15s/it]
 48%|████▊     | 16/33 [00:18<00:18,  1.09s/it]
 52%|█████▏    | 17/33 [00:19<00:17,  1.08s/it]
 55%|█████▍    | 18/33 [00:20<00:15,  1.02s/it]
 58%|█████▊    | 19/33 [00:21<00:13,  1.04it/s]
 61%|██████    | 20/33 [00:22<00:12,  1.03it/s]
 64%|██████▎   | 21/33 [00:23<00:12,  1.05s/it]
 67%|██████▋   | 22/33 [00:24<00:11,  1.08s/it]
 70%|██████▉   | 23/33 [00:25<00:10,  1.07s/it]
 73%|███████▎  | 24/33 [00:26<00:09,  1.03s/it]
 76%|███████▌  | 25/33 [00:27<00:08,  1.06s/it]
 79%|███████▉  | 26/33 [00:28<00:07,  1.04s/it]
 82%|████████▏ | 27/33 [00:29<00:06,  1.02s/it]
 85%|████████▍ | 28/33 [00:31<00:05,  1.06s/it]
 88%|████████▊ | 29/33 [00:32<00:04,  1.12s/it]
 91%|█████████ | 30/33 [00:33<00:03,  1.16s/it]
 94%|█████████▍| 31/33 [00:34<00:02,  1.10s/it]
 97%|█████████▋| 32/33 [00:35<00:01,  1.08s/it]
100%|██████████| 33/33 [00:36<00:00,  1.11s/it]
100%|██████████| 33/33 [00:36<00:00,  1.12s/it][INFO] Found sidecar_path in config: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] tensor_to_frame called, sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] sidecar_save_channel_minmax called, file_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[sidecar_save_channel_minmax] 保存 184 个通道的min/max到: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] encode_ftensors: sidecar_path=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
Submitted job_id [000] completed
[INFO] Found sidecar_path in config: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
Submitted job_id [000] completed

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]

[DEBUG] 使用每通道min/max进行反归一化！
[DEBUG] 使用sidecar文件: /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sidecar_data
[DEBUG] 通道数量: 184, 加载的通道统计信息数量: 184
[DEBUG] 前5个通道的min/max值: [[-2.38626289  1.30510497]
 [-0.21518801  0.12610497]
 [-0.11327328  0.19241317]
 [-0.33342656  0.38742507]
 [-0.18478705  0.2936613 ]]
[2025-06-11 11:26:12,486][VideoSplitInference][INFO] - Processing NN-Part2...

--> job_id [000] Running: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/bin/EncoderAppStatic -i /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sfu-hw-Traffic_2560x1600_30_val_qp16_200x272_30fps_10bit_p400_input.yuv -c /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/cfg/encoder_lowdelay_vtm.cfg -q 16 -o /dev/null -wdt 200 -hgt 272 -fr 30 -ts 1 -dph 0 --IntraPeriod=32 --InputChromaFormat=400 --InputBitDepth=10 --ConformanceWindowMode=1 --Level=5.1 --DecodingRefreshType=1 --BitstreamFile=/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sfu-hw-Traffic_2560x1600_30_val_qp16_200x272_30fps_10bit_p400.bin --FramesToBeEncoded=33
--> job_id [000] Running: /work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3/bin/DecoderAppStatic -b /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sfu-hw-Traffic_2560x1600_30_val_qp16_tmp.bin -o /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/codec_output/sfu-hw-Traffic_2560x1600_30_val_qp16_dec.yuv

  0%|          | 0/33 [00:00<?, ?it/s]/work/Users/<USER>/anaconda/envs/fctm7.0/lib/python3.8/site-packages/torch/functional.py:504: UserWarning: torch.meshgrid: in an upcoming release, it will be required to pass the indexing argument. (Triggered internally at ../aten/src/ATen/native/TensorShape.cpp:3483.)
  return _VF.meshgrid(tensors, **kwargs)  # type: ignore[attr-defined]

  3%|▎         | 1/33 [00:00<00:17,  1.82it/s]
  6%|▌         | 2/33 [00:01<00:16,  1.87it/s]
  9%|▉         | 3/33 [00:01<00:15,  1.90it/s]
 12%|█▏        | 4/33 [00:02<00:15,  1.91it/s]
 15%|█▌        | 5/33 [00:02<00:14,  1.91it/s]
 18%|█▊        | 6/33 [00:03<00:14,  1.91it/s]
 21%|██        | 7/33 [00:03<00:13,  1.91it/s]
 24%|██▍       | 8/33 [00:04<00:13,  1.92it/s]
 27%|██▋       | 9/33 [00:04<00:12,  1.91it/s]
 30%|███       | 10/33 [00:05<00:12,  1.92it/s]
 33%|███▎      | 11/33 [00:05<00:11,  1.90it/s]
 36%|███▋      | 12/33 [00:06<00:11,  1.91it/s]
 39%|███▉      | 13/33 [00:06<00:10,  1.89it/s]
 42%|████▏     | 14/33 [00:07<00:10,  1.89it/s]
 45%|████▌     | 15/33 [00:07<00:09,  1.89it/s]
 48%|████▊     | 16/33 [00:08<00:09,  1.81it/s]
 52%|█████▏    | 17/33 [00:09<00:08,  1.80it/s]
 55%|█████▍    | 18/33 [00:09<00:08,  1.79it/s]
 58%|█████▊    | 19/33 [00:10<00:07,  1.78it/s]
 61%|██████    | 20/33 [00:10<00:07,  1.79it/s]
 64%|██████▎   | 21/33 [00:11<00:06,  1.77it/s]
 67%|██████▋   | 22/33 [00:11<00:06,  1.79it/s]
 70%|██████▉   | 23/33 [00:12<00:05,  1.80it/s]
 73%|███████▎  | 24/33 [00:12<00:04,  1.81it/s]
 76%|███████▌  | 25/33 [00:13<00:04,  1.82it/s]
 79%|███████▉  | 26/33 [00:14<00:03,  1.82it/s]
 82%|████████▏ | 27/33 [00:14<00:03,  1.82it/s]
 85%|████████▍ | 28/33 [00:15<00:02,  1.81it/s]
 88%|████████▊ | 29/33 [00:15<00:02,  1.80it/s]
 91%|█████████ | 30/33 [00:16<00:01,  1.80it/s]
 94%|█████████▍| 31/33 [00:16<00:01,  1.81it/s]
 97%|█████████▋| 32/33 [00:17<00:00,  1.82it/s]
100%|██████████| 33/33 [00:17<00:00,  1.79it/s]
100%|██████████| 33/33 [00:18<00:00,  1.83it/s][2025-06-11 11:26:30,523][detectron2.evaluation.coco_evaluation][INFO] - Preparing results for COCO format ...
[2025-06-11 11:26:30,524][detectron2.evaluation.coco_evaluation][INFO] - Saving results to /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/evaluation/coco_instances_results.json
[2025-06-11 11:26:30,537][detectron2.evaluation.coco_evaluation][INFO] - Evaluating predictions with official COCO API...
Loading and preparing results...
DONE (t=0.00s)
creating index...
index created!
Running per image evaluation...
Evaluate annotation type *bbox*
DONE (t=2.38s).
Accumulating evaluation results...
DONE (t=0.04s).
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.619
 Average Precision  (AP) @[ IoU=0.50      | area=   all | maxDets=100 ] = 0.788
 Average Precision  (AP) @[ IoU=0.75      | area=   all | maxDets=100 ] = 0.699
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.000
 Average Precision  (AP) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.226
 Average Precision  (AP) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.797
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=  1 ] = 0.456
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets= 10 ] = 0.493
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=   all | maxDets=100 ] = 0.653
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= small | maxDets=100 ] = 0.000
 Average Recall     (AR) @[ IoU=0.50:0.95 | area=medium | maxDets=100 ] = 0.271
 Average Recall     (AR) @[ IoU=0.50:0.95 | area= large | maxDets=100 ] = 0.838
[2025-06-11 11:26:32,962][detectron2.evaluation.coco_evaluation][INFO] - Evaluation results for bbox: 
|   AP   |  AP50  |  AP75  |  APs  |  APm   |  APl   |
|:------:|:------:|:------:|:-----:|:------:|:------:|
| 61.856 | 78.789 | 69.861 | 0.000 | 22.592 | 79.746 |
[2025-06-11 11:26:32,964][detectron2.evaluation.coco_evaluation][INFO] - Per-category bbox AP: 
| category      | AP   | category     | AP   | category       | AP     |
|:--------------|:-----|:-------------|:-----|:---------------|:-------|
| person        | nan  | bicycle      | nan  | car            | 36.526 |
| motorcycle    | nan  | airplane     | nan  | bus            | 87.185 |
| train         | nan  | truck        | nan  | boat           | nan    |
| traffic light | nan  | fire hydrant | nan  | stop sign      | nan    |
| parking meter | nan  | bench        | nan  | bird           | nan    |
| cat           | nan  | dog          | nan  | horse          | nan    |
| sheep         | nan  | cow          | nan  | elephant       | nan    |
| bear          | nan  | zebra        | nan  | giraffe        | nan    |
| backpack      | nan  | umbrella     | nan  | handbag        | nan    |
| tie           | nan  | suitcase     | nan  | frisbee        | nan    |
| skis          | nan  | snowboard    | nan  | sports ball    | nan    |
| kite          | nan  | baseball bat | nan  | baseball glove | nan    |
| skateboard    | nan  | surfboard    | nan  | tennis racket  | nan    |
| bottle        | nan  | wine glass   | nan  | cup            | nan    |
| fork          | nan  | knife        | nan  | spoon          | nan    |
| bowl          | nan  | banana       | nan  | apple          | nan    |
| sandwich      | nan  | orange       | nan  | broccoli       | nan    |
| carrot        | nan  | hot dog      | nan  | pizza          | nan    |
| donut         | nan  | cake         | nan  | chair          | nan    |
| couch         | nan  | potted plant | nan  | bed            | nan    |
| dining table  | nan  | toilet       | nan  | tv             | nan    |
| laptop        | nan  | mouse        | nan  | remote         | nan    |
| keyboard      | nan  | cell phone   | nan  | microwave      | nan    |
| oven          | nan  | toaster      | nan  | sink           | nan    |
| refrigerator  | nan  | book         | nan  | clock          | nan    |
| vase          | nan  | scissors     | nan  | teddy bear     | nan    |
| hair drier    | nan  | toothbrush   | nan  |                |        |
====================================================================================================
Encoding Information [Top 5 Rows...][VideoSplitInference()]
╒════╤══════════════════╤══════════════╤══════════════════════════════════╤══════╤═════════╤═══════════════╤══════════════════╤════════════════╕
│    │  input_datatype  │  input_size  │            file_name             │   qp │   bytes │   coded_order │  org_input_size  │   total_pixels │
╞════╪══════════════════╪══════════════╪══════════════════════════════════╪══════╪═════════╪═══════════════╪══════════════════╪════════════════╡
│  0 │  torch.float32   │ (800, 1280)  │ Traffic_2560x1600_30_val_000.png │   16 │ 12401.7 │             0 │    1600x2560     │        4096000 │
├────┼──────────────────┼──────────────┼──────────────────────────────────┼──────┼─────────┼───────────────┼──────────────────┼────────────────┤
│  1 │  torch.float32   │ (800, 1280)  │ Traffic_2560x1600_30_val_001.png │   16 │ 12401.7 │             1 │    1600x2560     │        4096000 │
├────┼──────────────────┼──────────────┼──────────────────────────────────┼──────┼─────────┼───────────────┼──────────────────┼────────────────┤
│  2 │  torch.float32   │ (800, 1280)  │ Traffic_2560x1600_30_val_002.png │   16 │ 12401.7 │             2 │    1600x2560     │        4096000 │
├────┼──────────────────┼──────────────┼──────────────────────────────────┼──────┼─────────┼───────────────┼──────────────────┼────────────────┤
│  3 │  torch.float32   │ (800, 1280)  │ Traffic_2560x1600_30_val_003.png │   16 │ 12401.7 │             3 │    1600x2560     │        4096000 │
├────┼──────────────────┼──────────────┼──────────────────────────────────┼──────┼─────────┼───────────────┼──────────────────┼────────────────┤
│  4 │  torch.float32   │ (800, 1280)  │ Traffic_2560x1600_30_val_004.png │   16 │ 12401.7 │             4 │    1600x2560     │        4096000 │
╘════╧══════════════════╧══════════════╧══════════════════════════════════╧══════╧═════════╧═══════════════╧══════════════════╧════════════════╛

Summary files saved in : /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output/split-inference-video/fctm_original/SFUHW/sfu-hw-Traffic_2560x1600_30_val/qp16/evaluation


Performance Metrics Using Evaluation Criteria AP

Frame Rate: 30, Total Frame: 33
+----+----------------------+-------+----------------------+------+------------------+----------------+-------------+----------+----------------------------+---------------------+----------------------+----------+----------------------+---------------------+------------------------------+-------------+
|    | Dataset              |   fps |   num_of_coded_frame |   qp |   bitrate (kbps) |   end_accuracy |   nn_part_1 |   encode |   encode_feature_reduction |   encode_conversion |   encode_inner_codec |   decode |   decode_inner_codec |   decode_conversion |   decode_feature_restoration |   nn_part_2 |
|----+----------------------+-------+----------------------+------+------------------+----------------+-------------+----------+----------------------------+---------------------+----------------------+----------+----------------------+---------------------+------------------------------+-------------|
|  0 | Traffic_2560x1600_30 |    30 |                   33 |   16 |          2976.41 |        61.8557 |     36.4064 |  506.653 |                    2.32571 |            0.356324 |              503.729 |  10.5217 |             0.542463 |            0.287527 |                      9.66244 |     17.9256 |
+----+----------------------+-------+----------------------+------+------------------+----------------+-------------+----------+----------------------------+---------------------+----------------------+----------+----------------------+---------------------+------------------------------+-------------+

任务完成
编码完成！
如需执行解码评估，请运行对应的解码评估脚本。
