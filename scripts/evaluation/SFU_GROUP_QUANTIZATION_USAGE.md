# SFU数据集分组量化功能使用指南

## 概述

本文档介绍如何在SFU数据集上使用FCTM 7.0的分组量化功能。分组量化功能已成功集成到`run_all.py`脚本中，可以通过简单的配置开关来启用。

## 功能特点

### 🎯 智能分组策略
- 基于动态范围统计学分析的6组分类
- 自适应归一化，提高量化精度
- 详细的分组信息记录和分析

### 📊 完整的实验支持
- 支持所有SFU数据集序列（Class A/B/C/D）
- 支持多QP点测试
- 自动生成分组统计信息

### 🔧 易于使用
- 一键开关控制
- 自动目录管理
- 完整的结果分析工具

## 使用方法

### 1. 启用分组量化实验

编辑 `scripts/evaluation/run_all.py` 文件：

```python
def main():
    # 基本配置
    output_dir = "/work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output"
    dataset_dir = "/work/Users/<USER>/fcm_testdata"
    vtm_dir = "/work/Users/<USER>/fctm-v6.1/VVCSoftware_VTM-VTM-23.3"
    
    # 实验配置
    enable_group_quantization = True  # 🔥 设置为True启用分组量化
    
    # 运行测试
    test_sfu(output_dir, output_name, dataset_dir, vtm_dir, enable_group_quantization=enable_group_quantization)
```

### 2. 运行实验

```bash
cd /work/Users/<USER>/fctm-v7.0/scripts/evaluation
conda activate fctm7.0
python run_all.py
```

### 3. 分析结果

```bash
python analyze_group_quantization_results.py
```

## 实验配置

### SFU数据集序列列表

| 类别 | 序列名称 | 分辨率 | 帧率 | QP设置 |
|------|----------|--------|------|--------|
| **Class A/B** | Traffic_2560x1600_30_val | 2560×1600 | 30fps | [7, 10, 16, 19] |
| | Kimono_1920x1080_24_val | 1920×1080 | 24fps | [13, 15, 21, 23] |
| | ParkScene_1920x1080_24_val | 1920×1080 | 24fps | [10, 11, 12, 16] |
| | Cactus_1920x1080_50_val | 1920×1080 | 50fps | [14, 21, 24, 27] |
| | BasketballDrive_1920x1080_50_val | 1920×1080 | 50fps | [4, 9, 12, 16] |
| | BQTerrace_1920x1080_60_val | 1920×1080 | 60fps | [7, 9, 10, 13] |
| **Class C** | BasketballDrill_832x480_50_val | 832×480 | 50fps | [10, 14, 16, 21] |
| | BQMall_832x480_60_val | 832×480 | 60fps | [5, 12, 22, 24] |
| | PartyScene_832x480_50_val | 832×480 | 50fps | [11, 13, 16, 19] |
| | RaceHorses_832x480_30_val | 832×480 | 30fps | [19, 24, 26, 29] |
| **Class D** | BasketballPass_416x240_50_val | 416×240 | 50fps | [10, 19, 22, 24] |
| | BQSquare_416x240_60_val | 416×240 | 60fps | [12, 18, 22, 24] |
| | BlowingBubbles_416x240_50_val | 416×240 | 50fps | [18, 22, 24, 26] |
| | RaceHorses_416x240_30_val | 416×240 | 30fps | [24, 28, 31, 33] |

### 分组量化策略

| 分组名称 | 范围 | 统计学含义 | 通道特征 |
|---------|------|-----------|----------|
| **ultra_low** | < μ-2σ | 极低动态范围 | 几乎平坦，变化极小 |
| **low** | μ-2σ ~ μ-σ | 低动态范围 | 变化平缓 |
| **medium_low** | μ-σ ~ μ | 中低动态范围 | 略低于平均水平 |
| **medium_high** | μ ~ μ+σ | 中高动态范围 | 略高于平均水平 |
| **high** | μ+σ ~ μ+2σ | 高动态范围 | 变化剧烈 |
| **ultra_high** | > μ+2σ | 超高动态范围 | 包含重要细节 |

## 输出文件结构

```
fctm_output/
└── split-inference-video/
    └── fctmfcm7.0_group_quant_SFU/
        └── SFUHW/
            └── sfu-hw-{序列名}/
                └── qp{QP值}/
                    └── codec_output/
                        ├── group_quantization_data/
                        │   └── group_info.npy          # 🔥 分组信息文件
                        └── sidecar_data/
                            └── minmax.npy              # 每通道量化信息
```

### group_info.npy 文件格式

```python
{
    'frames': [
        {
            'frame_index': 0,
            'mean_range': 3.005036,      # 动态范围均值
            'std_range': 4.482265,       # 动态范围标准差
            'groups': {
                'ultra_low': {
                    'count': 0,              # 该组通道数量
                    'channels': [],          # 通道索引列表
                    'global_min': 0.0,       # 组内全局最小值
                    'global_max': 0.0,       # 组内全局最大值
                    'range_boundary': [...]  # 分组边界
                },
                # ... 其他分组
            }
        },
        # ... 其他帧
    ]
}
```

## 结果分析

### 自动分析工具

运行分析脚本：
```bash
python analyze_group_quantization_results.py
```

### 生成的分析内容

1. **分组分布统计**: 各分组的平均通道数、标准差、范围
2. **QP影响分析**: 不同QP对分组效果的影响
3. **可视化图表**: 
   - 分组分布箱线图
   - 序列对比热力图
4. **汇总报告**: 完整的实验统计信息

### 分析输出位置

```
fctm_output/
├── group_quantization_analysis/
│   ├── group_distribution_boxplot.png    # 分组分布图
│   └── sequence_group_heatmap.png         # 序列对比热力图
└── group_quantization_summary.txt         # 汇总报告
```

## 实验对比

### 实验模式对比

| 模式 | 实验名称 | 量化策略 | 输出目录 |
|------|----------|----------|----------|
| **分组量化** | `fcm7.0_group_quant_SFU` | 6组统计学分组 | `fctmfcm7.0_group_quant_SFU` |
| **标准模式** | `fcm7.0_ori_SFU` | 全局min/max | `fctmfcm7.0_ori_SFU` |

### 性能预期

- **量化精度**: 分组量化预期提供更好的量化精度
- **码率效率**: 通过智能分组优化码率分配
- **计算开销**: 增加少量分组分析计算

## 故障排除

### 常见问题

1. **目录权限问题**
   ```bash
   chmod -R 755 /work/Users/<USER>/fctm-v7.0/scripts/evaluation/fctm_output
   ```

2. **conda环境问题**
   ```bash
   conda activate fctm7.0
   conda list | grep torch  # 验证环境
   ```

3. **分组信息文件缺失**
   - 检查`group_quantization_path`参数是否正确传递
   - 确认目录创建权限

### 调试模式

在`run_all.py`中启用详细日志：
```python
print(f"[DEBUG] 分组量化目录: {group_quantization_dir}")
print(f"[DEBUG] 任务命令: {task_command}")
```

## 总结

SFU数据集分组量化功能已完全集成到FCTM 7.0评估框架中，提供：

✅ **一键启用**: 通过`enable_group_quantization`开关控制  
✅ **完整支持**: 支持所有SFU序列和QP配置  
✅ **自动分析**: 内置结果分析和可视化工具  
✅ **向后兼容**: 不影响现有标准实验流程  

通过这个功能，可以深入分析不同视频内容的通道特征分布，为特征编码优化提供重要参考。

---

**版本**: FCTM 7.0  
**作者**: tianshuchang  
**日期**: 2025-07-14
